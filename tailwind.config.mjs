/**
 * @params {number} from
 * @params {number} to
 * @returns {Record<string, string>}
 */
function generateNumUnits(from, to, step = 1) {
  /** @type {any} */
  const ret = {};
  for (let i = from; i <= to; i += step) {
    ret[i] = i ? i + "px" : i;
  }
  return ret;
}

/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
  theme: {
    spacing: {
      ...generateNumUnits(0, 1000),
    },
    extend: {
      borderRadius: generateNumUnits(1, 30),
      borderWidth: generateNumUnits(1, 30),
      zIndex: {
        1: 1,
      },
    },
    fontSize: generateNumUnits(0, 60, 1),
    lineHeight: generateNumUnits(10, 60),
  },
  plugins: [],
};
