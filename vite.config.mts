import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";

// https://vitejs.dev/config/
export default defineConfig({
  base: "/mucang-chat",
  plugins: [vue()],
  resolve: {
    alias: {
      "@": "/src",
    },
  },
  build: {
    rollupOptions: {
      input: {
        main: "index.html",
        stuinfo: "stuinfo.html",
      },
    },
  },
  server: {
    host: "0.0.0.0",
  },
});
