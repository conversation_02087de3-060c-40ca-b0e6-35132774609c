@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  margin: 0;
  height: 100%;
  overflow: hidden;
}

::-webkit-scrollbar {
  width: 4px;
  height: 3px;
  background-color: #333;
}

/*滚动条的轨道*/
::-webkit-scrollbar-track {
  background-color: white;
}

/*滚动条的滑块按钮*/
::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background-color: rgba(168, 184, 196, 0.4);
}

.scroller-none::-webkit-scrollbar {
  display: none;
  width: 0;
}

svg {
  width: 100%;
  height: 100%;
}

svg path {
  fill: currentColor;
  /* stroke: currentColor; */
}

svg path[stroke] {
  fill: unset;
  stroke: currentColor;
}

svg[stroke] {
  stroke: currentColor;
}

.loadingGif {
  background: url(https://web-resource.mc-cdn.cn/web/jkbd-default/loading.gif)
    no-repeat center;
  background-size: 300px auto;
}

/* 这一块必须放全局样式，因为打包有问题，把样式搞没了 */

.pageStack {
  position: relative;
  width: 100vw;
  height: 100vh;
}

.page {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: white;
}

.push-enter-active,
.push-leave-active {
  transition: transform 0.3s;
}

.push-enter-from {
  transform: translateX(100%);
}

.push-leave-to {
  transform: translateX(-50%);
}

.back-enter-active,
.back-leave-active {
  transition: transform 0.3s;
}

.back-leave-to {
  transform: translateX(100%);
}

.back-enter-from {
  transform: translateX(-50%);
}
