<template>
  <div class="w-screen h-screen" :class="{ loadingGif: !!src }">
    <iframe
      id="myIframe"
      :srcdoc="srcdoc"
      :src="src"
      width="100%"
      height="100%"
      frameborder="0"
    ></iframe>
  </div>
</template>
<script setup lang="ts">
import { useRoute } from "vue-router";
const route = useRoute();

const src = route.query.src as string | undefined;
const srcdoc = route.query.srcdoc as string | undefined;
</script>
