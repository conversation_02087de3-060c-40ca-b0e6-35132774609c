<template>
  <div class="h-[100vh] flex flex-col bg-[#E6F2FB]">
    <HeadWin
      :user-avatar="userAvatar"
      :user-info="userInfo"
      @refresh="sessionInstance?.refresh()"
    ></HeadWin>
    <div class="flex flex-1 h-0">
      <Sessions
        @select-session="onSelectSession"
        :sse="sseEventEmitter"
        ref="sessionInstance"
      />
      <ChatWin
        v-if="session"
        :sse="sseEventEmitter"
        @remove-current="sessionInstance?.removeCurrent()"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import Sessions from "@/components/Sessions.vue";
import ChatWin from "@/components/Chat/Win.vue";
import HeadWin from "@/components/Head/Win.vue";
import { useHome } from "../home";
import { ref } from "vue";
const { userInfo, userAvatar, sseEventEmitter, session, onSelectSession } =
  useHome();

const sessionInstance = ref<InstanceType<typeof Sessions>>();
</script>

<style lang="less" scoped>
.chat {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 100%;
}
</style>
