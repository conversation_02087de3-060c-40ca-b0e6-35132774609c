<template>
  <div class="relative">
    <div
      class="absolute top-0 left-0 text-gray-500"
      v-if="!modelValue && placeholder"
    >
      {{ placeholder }}
    </div>
    <div
      class="relative overflow-y-auto"
      :contenteditable="!readonly"
      @input="onInput"
      @compositionstart="onCompositionStart"
      @compositionend="onCompositionEnd"
      ref="richTextRef"
      v-bind="$attrs"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from "vue";

defineOptions({
  inheritAttrs: false,
});

const props = defineProps<{
  modelValue: string;
  placeholder?: string;
  rows?: number | string;
  readonly?: boolean;
}>();

const emit = defineEmits(["update:modelValue", "selectionchange"]);

const richTextRef = ref<HTMLElement | null>(null);

defineExpose({
  innerRef: richTextRef,
});

let inputLock = false;
const onCompositionStart = () => {
  console.log(1111);
  inputLock = true;
};
const onCompositionEnd = () => {
  console.log(2222);
  inputLock = false;
};

// 监听光标移动，输入文本后也会导致光标移动
const handleSelectionChange = () => {
  if (inputLock) {
    console.log("中文被锁定");
    return;
  }
  refreshContent();
};

onMounted(() => {
  document.addEventListener("selectionchange", handleSelectionChange);
});

onUnmounted(() => {
  document.removeEventListener("selectionchange", handleSelectionChange);
});

const refreshContent = () => {
  const target = richTextRef.value!;
  const value = target.innerHTML;
  const strimBr = value === "<br>" ? "" : value;
  internalValue.value = strimBr;

  // 删除到只有一个空行时，将内容设置为空字符串
  emit("update:modelValue", strimBr);
  emit("selectionchange");
};

// 当输入框的内容变化时触发
const onInput = () => {
  refreshContent();
};

function isFocused(element: HTMLElement) {
  return document.activeElement === element;
}
// 假设我们有一个方法来将光标定位到指定位置
function setCursorPosition(elem: HTMLElement) {
  if (!isFocused(elem)) {
    return;
  }
  console.log("设置内容后更新光标位置");
  const range = document.createRange();
  range.selectNodeContents(elem); //obj为元素节点
  range.collapse(false);
  var sel = window.getSelection()!;
  sel.removeAllRanges();
  sel.addRange(range);
}

const internalValue = ref("");

// 监听modelValue的变化，更新contenteditable元素的内容
// 这里如果是用户输入导致的变化，不能去修改，否则会导致光标跳到最前面去
// 如果是外部修改，则更新内容且将光标跳到最后面去
watch(
  () => props.modelValue !== internalValue.value,
  () => {
    richTextRef.value!.innerHTML = props.modelValue;
    setCursorPosition(richTextRef.value!);
  },
  {
    flush: "post", // 确保在DOM更新后执行
  },
);
onMounted(() => {
  internalValue.value = props.modelValue;
  richTextRef.value!.innerHTML = props.modelValue;
  setCursorPosition(richTextRef.value!);
});

const setHeight = () => {
  const lineHeight = parseFloat(
    window.getComputedStyle(richTextRef.value!).lineHeight,
  );
  richTextRef.value!.style.minHeight =
    (props.rows ? +props.rows : 1) * lineHeight + "px";
};

onMounted(() => {
  setHeight();
});
</script>
