<template>
  <div class="mx-14 relative">
    <div class="h-40 relative w-228 flex border-1 rounded-10 border-[#CCCFD0]">
      <i
        class="self-center ml-14 w-14 h-14 bg-cover bg-[url(./assets/top_search.png)]"
      ></i>
      <input
        class="px-8 flex-1 rounded-10 border-none outline-none text-[#333] text-14"
        placeholder="搜索"
        @keydown.enter.exact.prevent="startSearch"
        v-model="search"
      />
    </div>
    <Popup
      class="absolute left-0 w-full top-full mt-6"
      v-model="listSessionRes"
    >
      <div
        class="bg-white rounded-10 border border-[#cccfd0] shadow-md relative overflow-y-auto max-h-380"
      >
        <template
          v-if="
            listSessionRes?.singleChatList.length &&
            expandInfo.type !== 'groupChatList'
          "
        >
          <div
            class="pt-9 pl-14 pb-7 text-12/16 text-[#6e6e6e] sticky top-0 left-0 bg-white"
          >
            单聊会话
          </div>
          <div
            class="h-40 flex items-center px-14 cursor-pointer hover:bg-[rgba(4,165,255,0.2)]"
            v-for="item in listSessionRes?.singleChatList.slice(
              0,
              expandInfo.type ? undefined : expandInfo.singleChatCount,
            )"
            @click="goSession(item.sessionKey)"
          >
            <img
              class="w-30 h-30 object-cover rounded-full"
              :src="item.avatar"
            />
            <div
              class="ml-9 text-13/13 text-[#333] whitespace-nowrap overflow-hidden text-ellipsis"
              v-html="hightlightSearch(item.name, tempSearch)"
            ></div>
          </div>
          <div
            class="sticky left-0 bottom-0 pt-4 pb-10 cursor-pointer flex items-center justify-center bg-white"
            @click="expandInfo.type = expandInfo.type ? '' : 'singleChatList'"
          >
            <span class="text-12/16 text-[#a0a0a0]">{{
              expandInfo.type ? "收起" : "查看全部"
            }}</span>
            <i
              class="ml-3 w-9 h-12 bg-cover bg-[url(./assets/arrow.png)]"
              :class="{ 'rotate-180': !expandInfo.type }"
            ></i>
          </div>
        </template>
        <template
          v-if="
            listSessionRes?.groupChatList.length &&
            expandInfo.type !== 'singleChatList'
          "
        >
          <div
            class="pt-9 pl-14 pb-7 text-12/16 text-[#6e6e6e] sticky top-9 bg-white"
          >
            群聊会话
          </div>
          <div
            class="h-40 flex items-center px-14 cursor-pointer hover:bg-[rgba(4,165,255,0.2)]"
            v-for="item in listSessionRes?.groupChatList.slice(
              0,
              expandInfo.type ? undefined : expandInfo.groupChatCount,
            )"
            @click="goSession(item.sessionKey)"
          >
            <img
              class="w-30 h-30 object-cover rounded-full"
              :src="item.avatar"
            />
            <div
              class="ml-9 text-13/13 text-[#333] whitespace-nowrap overflow-hidden text-ellipsis"
              v-html="hightlightSearch(item.name, tempSearch)"
            ></div>
          </div>
          <div
            class="sticky left-0 bottom-0 pt-4 pb-10 cursor-pointer flex items-center justify-center bg-white"
            @click="expandInfo.type = expandInfo.type ? '' : 'groupChatList'"
          >
            <span class="text-12/16 text-[#a0a0a0]">{{
              expandInfo.type ? "收起" : "查看全部"
            }}</span>
            <i
              class="ml-3 w-9 h-12 bg-cover bg-[url(./assets/arrow.png)]"
              :class="{ 'rotate-180': !expandInfo.type }"
            ></i>
          </div>
        </template>
      </div>
    </Popup>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, inject } from "vue";
import { ListSessionRes, listSessions } from "@/api/chat";
import Popup from "../Popup.vue";
import { EventEmitter } from "@/utils/eventEmitter";
import { makeToast } from "@/utils/dom";

const search = ref("");
const listSessionRes = ref<ListSessionRes>();
const expandInfo = reactive({
  singleChatCount: 3,
  groupChatCount: 3,
  type: "" as "singleChatList" | "groupChatList" | "",
});
const emitter = inject<EventEmitter>("emitter")!;

function hightlightSearch(str: string, haystick: string) {
  return str.replace(
    haystick,
    `<span style="color: #04A5FF;">${haystick}</span>`,
  );
}

let tempSearch = "";
async function startSearch() {
  if (!search.value) {
    return;
  }
  const res = await listSessions({ name: search.value });

  if (!res.groupChatList.length && !res.singleChatList.length) {
    makeToast("没有任何搜索结果");
    return;
  }

  calcExpandInfo(res);

  tempSearch = search.value;
  listSessionRes.value = res;
}

/** 收起展开，一共最多展示6项 */
function calcExpandInfo(res: ListSessionRes) {
  let singleChatCount = res.singleChatList.length;
  let groupChatCount = res.groupChatList.length;
  if (singleChatCount + groupChatCount > 6) {
    if (singleChatCount < 3) {
      groupChatCount = 6 - singleChatCount;
    } else if (groupChatCount < 3) {
      singleChatCount = 6 - groupChatCount;
    } else {
      singleChatCount = groupChatCount = 3;
    }
  }
  Object.assign(expandInfo, {
    singleChatCount,
    groupChatCount,
    type: "",
  });
}

function goSession(sessionKey: string) {
  listSessionRes.value = undefined;
  emitter.emit("goSession", sessionKey);
}
</script>
