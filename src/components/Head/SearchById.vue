<template>
  <div class="relative">
    <div
      class="px-12 rounded-10 bg-[rgba(248,178,77,0.15)] text-[#FF822D] text-15/40 cursor-pointer"
      @click.stop="openPopup"
    >
      一键直达
    </div>
    <Popup class="absolute left-0 top-full mt-6" v-model="showSearchById">
      <div
        class="w-320 bg-white border border-[#cccfd0] rounded-10 flex flex-col shadow-md"
      >
        <div class="my-14 text-center text-14/20">输入木仓ID</div>
        <input
          ref="input"
          class="mx-14 px-18 text-14/40 rounded-9 bg-[#f9f9f9] placeholder:text-[#abafb1] outline-none"
          placeholder="请输入"
        />
        <div
          class="mt-10 mb-18 self-center px-24 bg-[#ff822d] rounded-9 text-14/40 text-white cursor-pointer"
          @click="openSession"
        >
          打开单聊会话
        </div>
      </div>
    </Popup>
  </div>
</template>

<script lang="ts" setup>
import { inject, ref, nextTick } from "vue";
import { getSessionByMucangId } from "@/api/chat";
import { makeToast } from "@/utils/dom";
import { EventEmitter } from "@/utils/eventEmitter";
import Popup from "../Popup.vue";

const showSearchById = ref(false);
const input = ref<HTMLInputElement>();
const emitter = inject<EventEmitter>("emitter")!;

function openPopup() {
  if (showSearchById.value) {
    showSearchById.value = false;
  } else {
    showSearchById.value = true;
    nextTick(() => {
      input.value!.focus();
    });
  }
}

async function openSession() {
  const mucangId = input.value!.value;
  if (!mucangId) {
    return;
  }
  const session = await getSessionByMucangId({ mucangId });
  if (!session) {
    makeToast("未找到对应用户的会话窗口");
  } else {
    showSearchById.value = false;
    emitter.emit("goSession", session);
  }
}
</script>
