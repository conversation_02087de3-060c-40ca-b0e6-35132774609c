<template>
  <div class="h-64 flex items-center p-12 bg-white z-1">
    <!-- <div class="text-20/40 font-bold">木仓聊天室</div> -->

    <SearchById />
    <SearchByName />
    <div
      class="w-30 h-30 cursor-pointer bg-cover bg-[url(./assets/top_Refresh.png)]"
      @click="$emit('refresh')"
    ></div>
    <TitleStatus />

    <div class="flex items-center">
      <div class="text-14/24 text-[#6E6E6E]">
        {{ userInfo.nickname }}
      </div>
      <img
        class="w-40 h-40 object-cover rounded-full ml-14"
        :src="userAvatar"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import SearchById from "./SearchById.vue";
import SearchByName from "./SearchByName.vue";
import TitleStatus from "./TitleStatus.vue";

defineEmits(["refresh"]);

defineProps<{
  userInfo: {
    username: string;
    nickname: string;
  };
  userAvatar: string;
}>();
</script>
