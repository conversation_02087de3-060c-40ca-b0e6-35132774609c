<!-- 蒙层 -->
<template>
  <Teleport to="body">
    <div
      class="fixed top-0 left-0 bottom-0 right-0 model-container flex items-start justify-center overflow-auto"
      :style="{
        backgroundColor: bg,
      }"
      v-if="visible"
      ref="modelContainerRef"
    >
      <div
        class="absolute right-2 top-2 cursor-pointer z-1 opacity-[.7]"
        @click="onClose"
        :class="{
          'w-60 h-60': !terminal,
          'w-[1rem] h-[1rem]': terminal,
        }"
        v-if="!noClose"
      >
        <div
          class="bg-[url(./assets/ic_close.png)] bg-cover w-full h-full"
        ></div>
      </div>
      <div
        class="rounded-lg bg-white shadow-xl"
        :class="{
          'mt-20': !terminal,
          'mt-[1rem]': terminal,
        }"
        :style="
          modelContentWidthRef
            ? { width: modelContentWidthRef + 'px' }
            : { width: 'auto' }
        "
      >
        <div
          class="overflow-auto scroller"
          style="max-height: calc(100vh - 20px)"
          ref="modelContentRef"
        >
          <slot></slot>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import {
  inject,
  onMounted,
  onUnmounted,
  ref,
  withDefaults,
  Teleport,
} from "vue";
import { debounce } from "lodash";
const props = withDefaults(
  defineProps<{
    bg?: string;
    noClose?: boolean;
  }>(),
  {
    bg: "rgba(0,0,0,.3)",
    noClose: false,
  },
);
const visible = defineModel<boolean>();
const modelContainerRef = ref<HTMLElement>();
const terminal = inject("terminal");
const modelContentRef = ref<HTMLElement>();
const modelContentWidthRef = ref<number>(0);

function clickOutSide(e: Event) {
  const target = e.target as HTMLElement;
  if (!(target instanceof HTMLElement)) {
    return;
  }
  if (
    visible.value &&
    modelContainerRef.value &&
    !target.className.includes("model-container")
  ) {
    return;
  }
  if (!props.noClose) {
    visible.value = false;
  }
}
const onClose = () => {
  visible.value = false;
};
onMounted(() => {
  window.addEventListener("resize", handleResize);
  document.addEventListener("click", clickOutSide, true);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  document.removeEventListener("click", clickOutSide, true);
});

const handleResize = debounce(() => {
  console.log("进入了");

  if (modelContentRef.value) {
    if (modelContentRef.value.clientWidth > window.innerWidth) {
      modelContentWidthRef.value = window.innerWidth;
    } else {
      console.log(modelContentRef.value.clientWidth);

      modelContentWidthRef.value = modelContentRef.value.clientWidth;
    }
    console.log(modelContentWidthRef.value);
  }
}, 500);
</script>

<style scoped lang="less">
.model-container {
  z-index: 1;
}

.scroller {
  &::-webkit-scrollbar {
    width: 4px;
    height: 3px;
    background-color: #333;
  }

  /*滚动条的轨道*/
  &::-webkit-scrollbar-track {
    background-color: #666;
  }

  /*滚动条的滑块按钮*/
  &::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background-color: white;
  }
}
</style>
