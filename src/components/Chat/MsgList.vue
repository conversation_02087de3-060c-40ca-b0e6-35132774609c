<template>
  <div
    class="flex-1 h-0 overflow-auto flex space-y-20 flex-col scroller-none bg-[#E6F2FB]"
    :class="{ 'p-5': terminal, 'p-20': !terminal }"
    ref="scrollerRef"
  >
    <div
      class=""
      v-for="msg in msgList.itemList.slice().reverse()"
      :key="msg.id"
    >
      <div
        class="text-center"
        :class="{ 'system-msg': terminal }"
        v-if="msg.msgType === 91"
      >
        <span class="text-blue-500">{{
          msg.staff ? msg.staff.nickname : msg.customer?.nickname
        }}</span>
        <span>进入了房间</span>
      </div>
      <div
        class="text-center"
        :class="{ 'system-msg': terminal }"
        v-else-if="msg.msgType === 93"
      >
        <span class="text-blue-500">{{
          msg.staff ? msg.staff.nickname : msg.customer?.nickname
        }}</span>
        <span>撤回了一条消息</span>
      </div>
      <div
        class="text-center"
        :class="{ 'system-msg': terminal }"
        v-else-if="msg.msgType === 99"
        v-html="formatClassSysMsg(msg)"
      />
      <div
        class="text-center"
        :class="{ 'system-msg': terminal }"
        v-else-if="msg.msgType === 92"
      >
        <span class="text-red-400">消息发送失败</span>
      </div>
      <div
        class="text-center"
        :class="{ 'system-msg': terminal }"
        v-else-if="msg.msgType === 200 || msg.msgType === 201"
      >
        <span class="text-blue-500">{{
          msg.msgType === 200
            ? msg.msgContent?.NewMemberList?.map((item) => item.nickName).join(
                "、",
              )
            : msg.msgContent?.ExitMemberList?.map((item) => item.nickName).join(
                "、",
              )
        }}</span>
        <span>{{ msg.msgType === 200 ? "加入群聊" : "退出群聊" }}</span>
      </div>
      <div
        class="text-center flex flex-col items-center"
        :class="{ 'system-msg': terminal }"
        v-else-if="msg.msgType === 202"
      >
        <div>
          <span class="text-blue-500">{{ msg.account?.name }}</span>
          <span>修改了群公告</span>
        </div>
        <div
          class="mt-[0.5em] max-w-[20em] px-[1em] py-[0.5em] rounded-6 text-gray-700 bg-amber-200 whitespace-pre-wrap"
        >
          {{ msg.msgContent?.content }}
        </div>
      </div>
      <div
        class="text-center flex flex-col items-center"
        :class="{ 'system-msg': terminal }"
        v-else-if="msg.msgType === 203"
      >
        <div>群聊已解散</div>
      </div>
      <template v-else>
        <div
          class="flex"
          :class="{
            content: terminal,
            'self-end flex-row-reverse': msg.staff,
          }"
          :key="msg.id"
        >
          <div
            :class="{ avatar: terminal, 'w-43 h-43 rounded-full': !terminal }"
            class="bg-cover"
            :style="{
              backgroundImage: `url(${msg.staff ? msg.staff.avatar : msg.customer?.avatar})`,
            }"
          ></div>

          <div
            class="flex flex-col mt-4 relative ml-5 mr-5"
            :class="msg.staff ? 'items-end' : ''"
          >
            <div
              class="flex -mt-8 text-gray-500 w-0 whitespace-nowrap gap-x-10 absolute items-center"
              :class="{
                msgTime: terminal,
                'text-12/18': !terminal,
                'justify-end': msg.staff,
              }"
            >
              <span v-if="session!.isGroup" :class="{ 'order-1': msg.staff }">{{
                msg.staff
                  ? msg.staff.nickname + " " + msg.account.name
                  : msg.customer?.nickname
              }}</span>

              <span
                v-else-if="msg.staff"
                class="order-1 text-14 text-[#565656]"
                >{{ msg.staff.nickname }}</span
              >

              <span class="text-[#aaaaaa]">{{
                formatSessionDate(msg.msgTime)
              }}</span>
            </div>
            <div class="flex items-start mt-10 gap-x-4">
              <div
                class="rounded-6 py-8 break-words whitespace-pre-wrap text-16"
                :class="{
                  'order-1': msg.staff,
                  'order-0': !msg.staff,
                  'describe px-[0.17rem]': terminal,
                  'max-w-378 px-17': !terminal,
                  '!max-w-500': !terminal && msg.msgType === 10021,
                  'bg-[#00a3ff] rounded-tl-12 rounded-br-12 rounded-tr-none rounded-bl-12 text-[#ffffff]':
                    msg.staff,
                  'bg-[#ffffff] rounded-tl-none rounded-br-12 rounded-tr-12 rounded-bl-12 text-[#565656]':
                    !msg.staff,
                }"
              >
                <MsgItem :msg="msg" :revert="!!msg.staff" />
              </div>
            </div>
            <div class="w-full h-25 mt-9" v-if="headleIsShowAI(msg)">
              <div
                class="w-86 h-25 bg-[url('./assets/tjhf.png')] bg-cover cursor-pointer absolute bottom-0 right-0"
                @click="onAIReply(msg)"
              ></div>
            </div>
          </div>
          <div
            class="flex flex-col justify-between text-gray-500"
            :class="{ 'text-12/30': terminal, 'text-13/30': !terminal }"
          >
            <div class="order-0 mt-15">
              <!-- 三个点 -->
              <MenuList
                :menu="getMenuList(msg)"
                :isInRight="!!msg.staff"
              ></MenuList>
            </div>
            <div
              class="text-[#aaaaaa] text-12/18"
              :class="{
                'mb-30': !msg.staff,
              }"
              v-if="msg.staff && msg.readReceipts"
            >
              已读
            </div>
          </div>
        </div>
        <!-- 引用区域 -->
        <div class="flex flex-col items-end">
          <!-- 回复框区域 -->
          <div
            class="bg-[#dce6f2] rounded-10 py-10 break-words mr-53 mt-7 text-[#565656] text-14 whitespace-nowrap overflow-hidden overflow-ellipsis"
            :class="{
              'msgTime max-w-[5rem] px-[0.17rem]': terminal,
              'text-12/18 max-w-378 px-17': !terminal,
              'self-end': msg.staff,
              'self-start ml-50': !msg.staff,
            }"
            v-if="msg.msgContent?.replyMsg"
          >
            <span>回复 {{ msg.msgContent.replyMsg.user?.nickName }}：</span>
            <span>{{
              getMsgHint({
                msgType: MsgTypeMapper[msg.msgContent.replyMsg.data?.type],
                msgContent: msg.msgContent.replyMsg.data?.content,
              } as any)
            }}</span>
          </div>
          <!-- 聊天框引用区域 -->
          <div
            class="bg-[#dce6f2] rounded-10 py-10 break-words mr-53 mt-7 text-[#565656]"
            :class="{
              'max-w-[5rem] msgTime px-[0.17rem]': terminal,
              'max-w-378 text-14 px-17': !terminal,
              'self-end': msg.staff,
              'self-start ml-50': !msg.staff,
            }"
            v-if="msg.msgContent?.refMsg"
          >
            {{
              getMsgHint({
                msgType: MsgTypeMapper[msg.msgContent.refMsg.data?.type],
                msgContent: msg.msgContent.refMsg.data?.content,
              } as any)
            }}
          </div>
        </div>
        <div
          class="italic text-gray-500 flex flex-col"
          :class="{
            'text-12/20 ': terminal,
            'text-13/30': !terminal,
            'items-end mr-50': msg.staff,
            'items-start ml-50': !msg.staff,
          }"
          v-if="msg.isRevoke"
        >
          已撤回
        </div>
      </template>
    </div>
  </div>
  <div class="relative">
    <div
      class="absolute cursor-pointer w-60 h-60 rounded-full bottom-10 right-10 bg-cover bg-center bg-[url(./assets/bottom.svg)]"
      v-if="showBackBottom"
      @click="onBackBottomClick"
    ></div>
  </div>
  <div
    class="absolute left-0 top-0 right-0 bottom-0 bg-gray-100 flex items-center justify-center"
    v-if="loading"
  >
    <div
      class="w-80 h-80 rounded-full border-white border-4 border-r-gray-200 animate-spin"
    ></div>
  </div>
  <AMapPopUp ref="aMapPopUpDomRef"></AMapPopUp>
</template>

<script lang="ts" setup>
import MsgItem from "@/components/MsgItem.vue";
import { Pagination, Msg, getMsgList, markMsgRead } from "@/api/chat";
import {
  ref,
  reactive,
  watch,
  nextTick,
  onMounted,
  inject,
  onUnmounted,
} from "vue";
import { dateFormat } from "@/utils/format";
import { EventEmitter } from "@/utils/eventEmitter";
import {
  getMsgHint,
  isGroupMemberMsg,
  isSales,
  isParrotSales,
  MsgTypeMapper,
} from "@/utils/helpers";
import { makeToast } from "@/utils/dom";
import MenuList from "./MenuList.vue";
import { useSessionStore } from "@/store/index";
import { storeToRefs } from "pinia";
import { assert } from "@/utils/tools";
import AMapPopUp from "@/components/Common/AMapPopUp/index.vue";

const props = defineProps<{
  sse?: EventEmitter;
}>();
const emit = defineEmits<{
  (e: "selectReferenceMsg", msg?: Msg, showAI?: boolean): void;
  (e: "AIReply", msg?: Msg, showAI?: boolean): void;
  (e: "revokeMsg", msg: Msg): void;
  (e: "replyMsg", msg: Msg): void;
  (e: "refMsg", msg: Msg): void;
  (e: "selectCustomer", customer: NonNullable<Msg["customer"]>): void;
}>();
defineExpose({
  appendNewMsgAfterSend(newMsg: Msg) {
    // 已经先通过sse收到该消息了
    if (!msgList.itemList.some((m) => m.id === newMsg.id)) {
      msgList.itemList.unshift(newMsg);
    }
    nextTick(() => autoScrollBottom(true));
  },
  autoScrollBottom: (smooth: boolean) => autoScrollBottom(smooth),
});

const { session } = storeToRefs(useSessionStore());

const terminal = inject("terminal");

const aMapPopUpDomRef = ref<InstanceType<typeof AMapPopUp>>();

const formatSessionDate = (date: number) =>
  dateFormat(date, "yyyy-MM-dd HH:mm");

const formatClassSysMsg = (msg: Msg) => {
  assert(msg.msgType === 99);
  return msg.msgContent?.text?.replace(
    /\{\{(.*?)\}\}/g,
    (match, key) =>
      `<span style="color:${msg.msgContent?.highlight || "rgb(59,130,246)"}">${msg.msgContent?.params?.[key.trim()] || match}</span>`,
  );
};

const headleIsShowAI = (msg: Msg): boolean => {
  return !!msg.customer && isParrotSales;
};

const scrollerRef = ref<HTMLElement>();
const autoScrollBottom = (smooth: boolean) => {
  const scroller = scrollerRef.value;
  if (!scroller) {
    return;
  }

  scroller.scrollBy({
    top: scroller.scrollHeight - scroller.scrollTop,
    behavior: smooth ? "smooth" : "instant",
  });
};
const keepScrollPosition = (setData: Function) => {
  const scroller = scrollerRef.value!;
  const scrollBottom = scroller.scrollHeight - scroller.scrollTop;
  setData();
  nextTick(() => {
    scroller.scrollTo({
      top: scroller.scrollHeight - scrollBottom,
      behavior: "instant",
    });
  });
};

const showBackBottom = ref(false);
const onBackBottomClick = () => {
  autoScrollBottom(false);
};
const onScroll = (e: Event) => {
  const scroller = e.target as HTMLElement;

  if (scroller.scrollTop === 0) {
    fetchMore();
  }

  const scrollBottom = scroller.scrollHeight - scroller.scrollTop;
  if (scrollBottom > window.screen.height * 4) {
    showBackBottom.value = true;
  } else {
    showBackBottom.value = false;
  }
};

const onAIReply = (msg: Msg) => {
  emit("selectReferenceMsg", msg, true);
  emit("AIReply", msg, true);
};

onMounted(() => {
  scrollerRef.value!.addEventListener("scroll", onScroll);
});

const msgList = reactive<Pagination<Msg>>({
  cursor: "",
  itemList: [],
  needClean: false,
  hasMore: false,
});

const loading = ref(false);
watch(
  () => session.value,
  async () => {
    if (!session.value) {
      return;
    }

    loading.value = true;
    const res = await getMsgList({ sessionKey: session.value!.sessionKey });
    // 切换session获取最新消息列表后，服务端会自动标记消息已读
    session.value.__markMsgRead && emitter.emit("markMsgRead");
    Object.assign(msgList, res);
    loading.value = false;
    nextTick(() => {
      const scroller = scrollerRef.value!;
      const whenAllImagesLoaded = Array.from(
        scroller.querySelectorAll("img"),
      ).map(
        (img) =>
          new Promise<any>((resolve) => {
            img.onload = resolve;
            img.onerror = resolve;
          }),
      );
      const whenAllVideosLoaded = Array.from(
        scroller.querySelectorAll("video"),
      ).map(
        (video) =>
          new Promise<any>((resolve) => {
            video.onloadedmetadata = resolve;
            video.onerror = resolve;
          }),
      );
      Promise.all(whenAllImagesLoaded.concat(whenAllVideosLoaded)).then(() =>
        autoScrollBottom(false),
      );
    });

    if (!isSales) {
      // 进来就生成
      emit("selectReferenceMsg", res.itemList[0]);
    }
  },
  { immediate: true },
);

let fetching = false;
const fetchMore = async () => {
  if (msgList.hasMore && !fetching) {
    fetching = true;
    const res = await getMsgList({
      sessionKey: session.value!.sessionKey,
      cursor: msgList.cursor,
    }).finally(() => {
      fetching = false;
    });
    // 记住距底部的滚动位置
    // FIXME: 会轻微闪一下
    keepScrollPosition(() => {
      Object.assign(msgList, {
        ...res,
        itemList: [...msgList.itemList, ...res.itemList],
      });
    });
  }
};

const emitter = inject<EventEmitter>("emitter")!;

function onNewMsg(msg: Msg) {
  if (msg.sessionKey !== session.value!.sessionKey) {
    return;
  }
  const existMsg = msgList.itemList.find((m) => m.id === msg.id);
  if (existMsg) {
    existMsg.isRevoke = msg.isRevoke;
    existMsg.readReceipts = msg.readReceipts;
    return;
  }

  if (msg.msgType === 203) {
    session.value!.isDismissed = true;
  }

  msgList.itemList.unshift(msg);

  const scroller = scrollerRef.value!;
  if (
    scroller &&
    (scroller.scrollHeight - scroller.scrollTop) / scroller.clientHeight > 2
  ) {
    // TODO: 滚出一屏以上，则提示有新消息
  } else {
    nextTick(() => autoScrollBottom(true));
  }

  markMsgRead({ sessionKey: session.value!.sessionKey });

  if (isGroupMemberMsg(msg)) {
    console.log("群成员更新了，刷新@列表");
    emitter.emit("refreshGroupMembers", session.value!);
  }
}

watch(
  () => props.sse,
  (sse) => {
    if (sse) {
      sse.off("message", onNewMsg);
      sse.on("message", onNewMsg);
    }
  },
  { immediate: true },
);

onUnmounted(() => {
  props.sse?.off("message", onNewMsg);
});

watch(
  () => session.value!,
  () => {
    showBackBottom.value = false;
  },
);

const getMenuList = (msg: Msg) => {
  const generalArr = [
    {
      label: "引用",
      onClick: () => emit("refMsg", msg),
    },
  ];
  if (msg.staff) {
    // 24小时内的可撤回
    if (new Date().getTime() - msg.msgTime < 24 * 60 * 60 * 1000) {
      generalArr.unshift({
        label: "撤回",
        onClick: () => emit("revokeMsg", msg),
      });
    }
    return generalArr;
  } else if (msg.customer) {
    const user = msg.customer;
    const items: {
      label: string;
      onClick: () => void;
    }[] = [
      {
        label: "回复",
        onClick: () => emit("replyMsg", msg),
      },
      {
        label: "复制",
        onClick: () => {
          try {
            navigator.clipboard.writeText(user.mucangId);
            makeToast("复制成功");
          } catch {
            makeToast("复制失败");
          }
        },
      },
      ...generalArr,
    ];
    if (session.value!.isGroup) {
      items.push({
        label: "查看用户画像",
        onClick: () => emit("selectCustomer", user),
      });
    }
    if (msg.msgType === MsgTypeMapper.location) {
      items.push({
        label: "学车地图",
        onClick: () => {
          console.log("msg", msg);
          aMapPopUpDomRef.value?.open(
            msg.sessionKey,
            msg.msgContent.latitude,
            msg.msgContent.longitude,
            msg.customer?.mucangId,
          );
        },
      });
    }
    return items;
  } else {
    return [];
  }
};
</script>

<style lang="less" scoped>
.content {
  font-size: 0.3rem;
  padding: 5px 5px;
}

.system-msg {
  font-size: 0.3rem;
  text-align: center;
}

.avatar {
  width: 0.7rem;
  height: 0.7rem;
  border-radius: 50%;
}

.describe {
  max-width: 5rem;
}

.msgTime {
  font-size: 0.21rem;
}
</style>
