<template>
  <RichText
    :ref="(v: any) => (inputRef = v.innerRef)"
    class="text-15/25 outline-none border-none resize-none max-h-200 atUsersInput"
    :readonly="sending"
    :style="{ height: inputHeight }"
    v-model="flyMsg"
    @keydown="handleKeyShortCut"
    @selectionchange="handleSelectionChange"
    rows="1"
    :placeholder="placeholder"
    autofocus
  ></RichText>

  <Teleport to="body" v-if="atState">
    <div
      class="fixed bg-white border border-gray-300 shadow-lg mb-2 rounded-6 w-200"
      :style="{ left: atState.x + 'px', bottom: atState.y + 'px' }"
    >
      <div class="p-8">群成员</div>
      <div
        class="max-h-600 overflow-y-auto whitespace-nowrap"
        ref="selectorEle"
      >
        <div
          class="flex items-center justify-between py-4 px-8"
          :class="{ 'text-red bg-gray-300': i === atState.selectIndex }"
          @mousedown.stop.prevent="selectMember(i)"
          v-for="(member, i) in filteredGroupMembers"
        >
          <img class="w-30 h-30 object-cover" :src="member.avatar" />
          <span>{{ member.nickName }}</span>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script lang="ts" setup>
const groupMembers = ref([
  {
    nickName: "所有人",
    avatar: "",
  },
  {
    nickName: "涂志昊",
    avatar: "",
  },
]);
const filteredGroupMembers = computed(() => {
  const text = atState.value?.text;
  if (!text) {
    return groupMembers.value;
  }

  return groupMembers.value.filter(
    (m, i) => i === 0 || m.nickName.includes(text),
  );
});
const atState = ref<{
  text: string;
  selectIndex: number;
  x: number;
  y: number;
} | null>(null);
const selectorEle = ref<HTMLDivElement>();

const handleKeyShortCut = (event: KeyboardEvent) => {
  console.log("按下按键", event.key);

  // 检查是否按下了ctrl键和d键
  // 检测是否按下了'd'键，不区分大小写
  if (event.key.toLowerCase() === "d") {
    // 检测操作系统

    // 根据操作系统判断是否按下了对应的快捷键
    if (isMac && event.metaKey) {
      // Mac系统下的Command + D
      console.log("Mac系统下的Command + D");
    } else if (!isMac && event.ctrlKey) {
      // Windows系统下的Ctrl + D
      console.log("Windows系统下的Ctrl + D");
    } else {
      return;
    }

    // 阻止默认行为，比如浏览器默认的保存为书签的行为
    event.preventDefault();
    // 执行你的逻辑
    emit("removeCurrent");
  } else if (atState.value) {
    // 检查按下的键是否是向上或向下箭头
    if (event.key === "ArrowUp" || event.key === "ArrowDown") {
      event.preventDefault(); // 阻止默认行为
      let currentIndex = atState.value.selectIndex;
      // 根据按下的键更新索引
      if (event.key === "ArrowUp") {
        currentIndex =
          currentIndex > 0
            ? currentIndex - 1
            : filteredGroupMembers.value.length - 1;
      } else if (event.key === "ArrowDown") {
        currentIndex =
          currentIndex < filteredGroupMembers.value.length - 1
            ? currentIndex + 1
            : 0;
      }
      atState.value.selectIndex = currentIndex;
      nextTick(() => {
        scrollIntoView(
          selectorEle.value!.children[currentIndex] as HTMLElement,
        );
      });
    } else if (event.key === "Enter") {
      // 检查按下的键是否是回车键
      event.preventDefault(); // 阻止默认行为，例如提交表单
      selectMember(atState.value.selectIndex);
    }
  } else if (event.key === "Enter" && !event.composed) {
    event.preventDefault();
    sendMessage();
  }
};

const handleSelectionChange = () => {
  console.log("光标改变");
  atState.value = null;

  const selection = window.getSelection();
  if (!selection || selection.rangeCount !== 1) {
    return;
  }
  const range = selection.getRangeAt(0);
  if (
    !inputRef.value ||
    !inputRef.value.contains(range.commonAncestorContainer) ||
    !range.collapsed ||
    range.startContainer.nodeType !== Node.TEXT_NODE
  ) {
    return;
  }

  const startPos = range.startOffset;
  const value = range.startContainer.nodeValue!;
  for (let i = startPos - 1; i >= 0; i--) {
    if (value[i].trim() !== value[i]) {
      console.log("检测到空格");
      return;
    } else if (value[i] === "@") {
      const br = range.getBoundingClientRect();
      atState.value = {
        text: value.substring(i + 1, startPos),
        selectIndex: 0,
        x: br.left,
        y: document.documentElement.clientHeight - br.top,
      };
      break;
    }
  }
};

const selectMember = (index: number) => {
  console.log(5555);
  atState.value = null;
  onPickUser(filteredGroupMembers.value[index]);
};

const onPickUser = (user: any, callback?: Function) => {
  const userName = user.nickName;

  // // 纯input
  // // 从当前鼠标位置往前找，找到@符号，将这一段替换
  // const inputEle = inputRef.value as HTMLInputElement;
  // const startPos = inputEle.selectionStart!;
  // const value = inputEle.value;
  // const beforeText = value.substring(0, startPos);

  // const atPos = beforeText.lastIndexOf("@");
  // if (atPos !== -1) {
  //   flyMsg.value = value.slice(0, atPos) + `@${userName}\u2005` + value.slice(startPos);
  //   inputEle.selectionStart = inputEle.selectionEnd = atPos + userName.length + 1;
  // }

  // 函数用于在当前光标位置插入文本
  function insertTextAtCursor() {
    const selection = window.getSelection()!;
    if (selection.rangeCount === 0) return; // 如果没有选择范围，则不执行任何操作

    const range = selection.getRangeAt(0);
    range.deleteContents(); // 删除当前选中的内容（如果有的话）

    assert(range.collapsed && range.startContainer.nodeType === Node.TEXT_NODE);
    const startPos = range.startOffset;
    const value = range.startContainer.nodeValue!;
    const beforeText = value.substring(0, startPos);
    const atPos = beforeText.lastIndexOf("@");

    if (atPos !== -1) {
      range.setStart(range.startContainer, atPos);
      range.deleteContents(); // 删除当前选中的内容（如果有的话）
      assert(
        range.collapsed && range.startContainer.nodeType === Node.TEXT_NODE,
      );
      // 创建一个文本节点
      const textNode = document.createElement("span");
      textNode.innerHTML = `@${userName}&nbsp;`;
      textNode.className = "text-blue-500";
      textNode.setAttribute("contenteditable", "false");
      textNode.setAttribute("data-user-name", userName);
      textNode.setAttribute("data-user-info", JSON.stringify(user));

      // 将文本节点插入到当前光标位置
      range.insertNode(textNode);
      // 清除当前的选择，并将光标移动到新插入的文本之后
      range.setStartAfter(textNode);
      range.setEndAfter(textNode);
      selection.removeAllRanges();
      selection.addRange(range);
    }
  }

  // 使用示例：在光标位置后插入 "Hello, World!"
  insertTextAtCursor();

  callback && callback();
};

const sendMessage = () => {
    const atInfo = {
      atUsers: [] as any[],
      isAtAll: false,
    };
    const doc = new DOMParser().parseFromString(flyMsg.value, "text/html");
    const text = Array.from(doc.body.childNodes)
      .map((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const ele = node as HTMLElement;
          if (ele.tagName.toLowerCase() === "span") {
            const userName = ele.getAttribute("data-user-name")!;
            if (userName === "所有人") {
              atInfo.isAtAll = true;
            } else {
              const userInfo = ele.getAttribute("data-user-info")!;
              atInfo.atUsers.push(JSON.parse(userInfo));
            }
            return `@${userName} `;
          } else if (ele.tagName.toLowerCase() === "br") {
            return "\n";
          }
        } else if (node.nodeType === Node.TEXT_NODE) {
          return node.nodeValue;
        }
      })
      .join("");
    // const text = flyMsg.value.replace(/@[^\s]+\u2005/g, (input, $1)=> {
    //   if ($1 === '所有人') {
    //     atInfo.isAtAll = true;
    //     atInfo.atUsers = [];
    //   } else {
    //     atInfo.atUsers.push(userName);
    //   }
    //   return `·${$1}~`;
    // });
}
</script>
