<!-- 会话列表上的tab栏 -->
<template>
  <div
    class="relative"
    :class="{
      'h-62': !terminal,
      'h-[1.2rem]': terminal,
    }"
  >
    <XScroll ref="xScrollRef">
      <div class="flex space-x-7 py-17" ref="sessionTabDomRef">
        <div
          v-for="item in sessionTab"
          :key="item.order"
          class="cursor-pointer flex-[1_auto_auto] whitespace-nowrap"
          :class="{
            activeTab: appStore.sessionTab === item.tagCode,
            normalTab: appStore.sessionTab !== item.tagCode,
            'text-[.3rem]/[.6rem]': terminal,
            'text-12/24': !terminal,
          }"
          @click="emit('switchTab', item.tagCode)"
        >
          {{ item.tagName }}
          {{
            item.unreadCount
              ? item.unreadCount > 999
                ? "999+"
                : item.unreadCount
              : null
          }}
        </div>
        <div class="flex-[0_0_43px]"></div>
      </div>
    </XScroll>
    <div
      class="tabManageBg w-59 h-full absolute right-0 top-2/4 -translate-y-1/2"
    >
      <div
        :class="{
          'w-26 h-26 ': !terminal,
          'w-[.6rem] h-[.6rem]': terminal,
        }"
        class="bg-[#ffffff] absolute z-1 right-0 top-1/2 -translate-y-1/2 rounded-17 cursor-pointer"
        @click.stop="onOpenClick"
      >
        <div
          :class="terminal ? 'w-[.2rem] h-[.2rem]' : 'w-11 h-9'"
          class="bg-[url('./assets/left_more.png')] bg-cover absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
        ></div>
      </div>
    </div>
    <!-- 弹窗区域 -->
    <Popup
      class="absolute zIndex bg-[#ffffff] border-[1px] border-solid border-[#cccfd0] rounded-10 max-h-350 overflow-auto"
      v-model="showPopup"
      :class="{
        'w-178': switchPop === 1 && !terminal,
        'w-286': switchPop === 2 && !terminal,
        'w-[3rem]': switchPop === 1 && terminal,
        'w-[5rem]': switchPop === 2 && terminal,
        'left-[50%] top-[80%]': !terminal,
        'left-1/2 -translate-x-1/2 top-full': terminal,
      }"
    >
      <div
        class="flex text-[#6e6e6e] justify-between px-14"
        :class="terminal ? 'h-[.8rem] text-[.3rem]' : ' h-32 text-12'"
      >
        <div class="flex items-center">
          {{ switchPop === 1 ? "聊天分组" : "管理分组" }}
        </div>
        <div
          class="flex items-center cursor-pointer"
          @click.stop="onManageClick"
          v-if="switchPop === 1"
        >
          <div>管理</div>
          <div
            class="bg-[url(./assets/arrow.png)] bg-cover rotate-90 ml-4"
            :class="terminal ? 'w-[.2rem] h-[.2rem]' : 'w-10 h-10'"
          ></div>
        </div>
      </div>
      <ChatGroup
        class="absolute z-1 right-0"
        :sessionTab="sessionTab"
        :selectedTab="selectedTab"
        @switch="onSwitchTab"
        v-if="switchPop === 1 && sessionTab"
      ></ChatGroup>
      <ManageGroup
        @confirm="onConfigClick"
        v-else-if="switchPop === 2"
        v-model:sessionTab="sessionTab"
        v-model:addSessionTab="addSessionTab"
        v-model:switchPop="switchPop"
      ></ManageGroup>
    </Popup>
  </div>
</template>

<script setup lang="ts">
import { useAppStore, useSseEventEmitterStore } from "@/store";
import { getSessionTab, Tabs, updateSessionTab } from "@/api/chat";
import { throttle } from "lodash";
import ChatGroup from "./ChatGroup.vue";
import ManageGroup from "./ManageGroup.vue";
import Popup from "./Popup.vue";
import XScroll from "./XScroll.vue";
import {
  computed,
  inject,
  nextTick,
  onMounted,
  onUnmounted,
  ref,
  watch,
} from "vue";
import { storeToRefs } from "pinia";
import { EventEmitter, useEventEmitter } from "@/utils/eventEmitter";

const appStore = useAppStore();
const sessionTab = ref<Set<Tabs>>();
const addSessionTab = ref<Set<Tabs>>();
const terminal = inject("terminal");
const sessionTabDomRef = ref<HTMLDivElement>();
const xScrollRef = ref<InstanceType<typeof XScroll>>();

const emit = defineEmits<{
  (e: "switchTab", sessionTab: string): void;
}>();
const switchPop = ref<1 | 2>(1); // 用于切换弹窗 1: 聊天分组 2: 管理分组
const showPopup = ref(false);

const selectedTab = computed({
  get: () => appStore.sessionTab,
  set: (val) => {
    emit("switchTab", val);
  },
});

const onOpenClick = async () => {
  showPopup.value = !showPopup.value;
  if (showPopup.value) {
    await getTab();
  }
};

const onManageClick = () => {
  switchPop.value = switchPop.value === 1 ? 2 : 1;
};

const getTab = async () => {
  const { invisibleTags, visibleTags } = await getSessionTab();
  sessionTab.value = new Set(visibleTags);
  addSessionTab.value = new Set(invisibleTags);
};

const onConfigClick = async () => {
  const tagCodes = Array.from(sessionTab.value || [])
    .map((item) => item.tagCode)
    .join(",");
  await updateSessionTab(tagCodes).catch((err) => {
    console.error("更新分组失败", err);
  });
};

const { sseEventEmitter } = storeToRefs(useSseEventEmitterStore());
const emitter = inject<EventEmitter>("emitter")!;

const regularGetTab = throttle(() => {
  getTab();
}, /** 服务端有15秒缓存 */ 7000);

useEventEmitter(emitter, "markMsgRead", () => {
  // 切换session获取最新消息列表后，服务端会自动标记消息已读
  getTab();
});

onMounted(async () => {
  await getTab();
  sseEventEmitter.value!.on("message", regularGetTab);
});

onUnmounted(() => {
  sseEventEmitter.value?.off("message", regularGetTab);
});

const onSwitchTab = (code: string, index: number) => {
  selectedTab.value = code;
  nextTick(() => {
    xScrollRef.value!.scrollIntoView(
      sessionTabDomRef.value!.children[index] as HTMLElement,
      {
        rightThreshold: 50,
      },
    );
  });
  showPopup.value = false;
};

watch(
  () => showPopup.value,
  () => {
    switchPop.value = 1;
  },
  {
    immediate: true,
  },
);
</script>

<style scoped lang="less">
.tabManageBg {
  background-image: linear-gradient(to right, transparent 0%, #e6f2fb 40%);
}

.normalTab {
  padding: 0 17px;
  border-radius: 9999px;
  color: #6e6e6e;
  background-color: white;
  display: flex;
  align-items: center;
}

.activeTab {
  padding: 0 17px;
  border-radius: 9999px;
  color: #04a5ff;
  background-color: rgba(4, 165, 255, 0.08);
  border: 1px solid rgba(4, 165, 255, 0.6);
  font-weight: bold;
  display: flex;
  align-items: center;
}

.chatGroup {
  background-color: #ffffff;
  width: 178px;
}

.zIndex {
  z-index: 1;
}
</style>
