<template>
  <div
    class="overflow-y-auto relative virtical-scrollbar"
    @scroll="onScroll"
    ref="containerRef"
    v-bind="$attrs"
  >
    <div :style="{ height: `${listHeight}px` }"></div>
    <div
      class="absolute top-0 left-0 right-4"
      :style="{ transform: `translateY(${visibleOffset}px)` }"
    >
      <slot
        v-for="item in visibleItems"
        :item="item"
        :key="itemKey(item)"
      ></slot>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from "vue";

const props = defineProps<{
  items: any[];
  itemHeight: number;
  itemKey: (item: any) => string;
}>();
const containerRef = ref<HTMLElement>();
const extraCount = 20;

// 使用 defineExpose 显式地暴露 containerRef
defineExpose({
  containerRef,
  scrollToItem: (item: any) => {
    const index = Math.max(0, props.items.indexOf(item));
    const itemTop = index * props.itemHeight;
    containerRef.value!.scrollTop = itemTop;
  },
});

const offset = ref(0);
const onScroll = (e: Event) => {
  const scroller = e.target as HTMLElement;
  offset.value = scroller.scrollTop;
};

const containerHeight = ref(0);
const recalcContainerHeight = () => {
  const container = containerRef.value!;
  containerHeight.value = container.clientHeight;
};

onMounted(recalcContainerHeight);

onMounted(() => {
  window.addEventListener("resize", recalcContainerHeight);
});

onUnmounted(() => {
  window.removeEventListener("resize", recalcContainerHeight);
});

const start = computed(() => {
  return Math.floor(offset.value / props.itemHeight);
});

const buffer = computed(() => {
  return Math.max(0, start.value - extraCount);
});

const listHeight = computed(() => {
  return props.items.length * props.itemHeight - props.itemHeight * extraCount;
});

const visibleOffset = computed(() => {
  return buffer.value * props.itemHeight;
});

const visibleItems = computed(() => {
  const visibleCount = Math.ceil(containerHeight.value / props.itemHeight);
  return props.items.slice(
    buffer.value,
    start.value + visibleCount + extraCount,
  );
});
</script>

<style lang="less" scoped></style>
