<template>
  <div class="scroll-container" v-resize="handleSizeChange">
    <div class="scroll" ref="xScrollRef" @mousedown="handleDragStart">
      <div class="content" :class="{ pointerEvents: isPointerRef }">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";

interface Resize {
  width: number;
  height: number;
}
const xScrollRef = ref<HTMLDivElement>();
// 拖拽相关状态
const isDraggingRef = ref(false);
const startXRef = ref(0);
const startScrollTopRef = ref(0);
const isPointerRef = ref(false);

const resize = reactive<Resize>({
  width: 0,
  height: 0,
});

const handleSizeChange = ({ width, height }: Resize) => {
  resize.width = width;
  resize.height = height;
};

// 拖拽处理逻辑
const handleDragStart = (e: MouseEvent) => {
  isDraggingRef.value = true;
  const clientX = e.clientX;
  startXRef.value = clientX;
  startScrollTopRef.value = xScrollRef.value?.scrollTop || 0;

  // 添加事件监听
  window.addEventListener("mousemove", handleDragMove);
  window.addEventListener("mouseup", handleDragEnd);
};

const handleDragMove = (e: MouseEvent) => {
  if (!isDraggingRef.value) return;
  isPointerRef.value = true;
  const clientX = e.clientX;
  const deltaX = clientX - startXRef.value;
  const newScrollTop = startScrollTopRef.value - deltaX;

  if (xScrollRef.value) {
    // 计算最大滚动范围
    const maxScrollTop =
      xScrollRef.value.scrollHeight - xScrollRef.value.clientHeight;
    const clampedScrollTop = Math.max(0, Math.min(newScrollTop, maxScrollTop));
    xScrollRef.value.scrollTop = clampedScrollTop;
  }
};

const handleDragEnd = () => {
  isDraggingRef.value = false;
  isPointerRef.value = false;
  // 移除事件监听
  window.removeEventListener("mousemove", handleDragMove);
  window.removeEventListener("mouseup", handleDragEnd);
};

function scrollIntoView(
  dom: HTMLElement,
  options?: {
    leftThreshold?: number;
    rightThreshold?: number;
  },
) {
  const { leftThreshold = 0, rightThreshold = 0 } = options || {};

  const scroller = xScrollRef.value!;

  const { scrollTop = 0 } = scroller;
  const scrollBottom = scrollTop + scroller.clientWidth;

  const { offsetLeft: offsetTop } = dom;
  const offsetBottom = offsetTop + dom.offsetWidth;

  const scrollOptions: { top: number } = { top: 0 };

  if (offsetTop - leftThreshold < scrollTop) {
    scrollOptions.top = offsetTop - leftThreshold - scrollTop;
  } else if (offsetBottom + rightThreshold > scrollBottom) {
    scrollOptions.top = offsetBottom + rightThreshold - scrollBottom;
  }

  scrollOptions.top += scrollTop;

  if (scroller.scrollTo) {
    scroller.scrollTo({
      ...scrollOptions,
      behavior: "smooth",
    });
  } else {
    // 低版本没有scroll方法
    scroller.scrollTop = scrollOptions.top;
  }
}

defineExpose({
  scrollIntoView,
});
</script>

<style scoped>
.scroll-container {
  width: 100%;
  height: 100%;
}
.scroll {
  --w: calc(v-bind("resize.width") * 1px);
  --h: calc(v-bind("resize.height") * 1px);
  height: var(--w);
  width: var(--h);
  position: relative;
  overflow: auto;
  transform-origin: left top;
  transform: translateY(var(--h)) rotate(-90deg);
}
.content {
  position: absolute;
  top: 0;
  left: var(--h);
  height: var(--h);
  transform-origin: left top;
  transform: rotate(90deg);
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
.pointerEvents {
  pointer-events: none;
}
</style>
