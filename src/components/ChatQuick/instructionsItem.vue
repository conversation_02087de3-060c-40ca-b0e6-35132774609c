<template>
  <div>
    <div
      class="flex flex-col text-13 w-full flex-wrap mt-8"
      v-if="msg && msg.msgType === 115"
    >
      <div class="flex-1 pt-8 flex">
        <div class="text-[#A0A0A0]">标题内容：</div>
        <div class="flex-1 text-[#464646]" v-html="msg.msgContent.title"></div>
      </div>
      <div class="flex-1 pt-8 flex">
        <div class="text-[#A0A0A0]">链接描述：</div>
        <div class="flex-1 text-[#464646]">{{ msg.msgContent.desc }}</div>
      </div>
      <div class="flex-1 pt-8 flex">
        <div class="text-[#A0A0A0]">跳转链接：</div>
        <div class="break-all flex-1 text-[#464646]">
          {{ msg.msgContent.url }}
        </div>
      </div>
      <div class="flex-1 pt-8 flex">
        <span class="text-[#A0A0A0]">卡片封面：</span>
        <div class="w-88 h-88">
          <img
            class="break-all object-fill"
            :src="
              HostNames.chat +
              '/api/h5/media/get.htm?encodedData=' +
              msg.msgContent.encodedData
            "
            alt=""
          />
        </div>
      </div>
    </div>
    <div
      class="flex flex-col text-13 w-full flex-wrap mt-8"
      v-else-if="msg && msg.msgType === 11"
    >
      <pre
        v-html="msg.msgContent.text"
        class="whitespace-pre-wrap [word-wrap:break-word]"
      ></pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { QuickMsgActionType } from "@/api/chat";
import { HostNames } from "@/utils/request";
defineProps<{
  msg: QuickMsgActionType | "";
}>();
</script>

<style scoped lang="less"></style>
