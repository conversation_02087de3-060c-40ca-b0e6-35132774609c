<!-- 意向表单组件 -->
<template>
  <div class="px-14">
    <div v-if="intentionFormList && intentionFormList.length > 0">
      <div class="mt-17 mb-17" v-for="item in intentionFormList" :key="item.id">
        <p
          class="font-bold text-[#333333]"
          :class="{
            'text-15': !terminal,
            'text-[.32rem]': terminal,
          }"
        >
          {{ item.submitTime }}
        </p>
        <div
          class="bg-[#fff] mt-8 p-13 rounded-5"
          :class="{
            'text-14': !terminal,
            'text-[.3rem]': terminal,
          }"
        >
          <div
            v-for="answersItem in item.answers"
            class="flex justify-between items-center space-y-7"
          >
            <p class="text-[#a0a0a0] w-100 mr-10">
              {{ answersItem.shortName }}
            </p>
            <p class="text-[#565656] flex-1 flex justify-end">
              {{ answersItem.option }}
            </p>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="flex justify-center items-center h-full">
      暂无意向表单
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, ref, watch } from "vue";
import { getIntentionFormList, IntentionFormAnswers } from "@/api/jxbm";
import { useSessionStore } from "@/store/index";
import { storeToRefs } from "pinia";
import { makeToast } from "@/utils/dom";
import { dateFormat } from "@/utils/format";

interface IntentionFormList {
  id: number;
  submitTime: string;
  answers: IntentionFormAnswers[];
}

const { session } = storeToRefs(useSessionStore());
const terminal = inject("terminal");

const intentionFormList = ref<IntentionFormList[]>();

watch(() => session.value!, getFormList, {
  immediate: true,
});

/**
 * 获取意向表单列表
 */
async function getFormList() {
  console.log(session.value);
  if (session.value?.customerMucangId) {
    try {
      const res = await getIntentionFormList({
        userId: session.value.customerMucangId,
      });

      intentionFormList.value = res.map((item) => {
        return {
          id: item.id,
          submitTime: dateFormat(item.createTime, "yyyy-MM-dd HH:mm:ss"),
          // submitTime: item.createTime,
          answers: JSON.parse(item.answers || "[]") as IntentionFormAnswers[],
        };
      });
      console.log(intentionFormList.value);
    } catch (error) {
      makeToast("获取意向表单失败");
      console.error(error);
    }
  } else {
    makeToast("没有木仓id");
  }
}
</script>

<style scoped lang="less"></style>
