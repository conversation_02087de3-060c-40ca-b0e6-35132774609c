<template>
  <div class="px-13 h-full">
    <GroupList
      title="常见问题快捷回复"
      :group-data-list="suggestionsByQA.groupList"
      :select-group="suggestionsByQA.selectedGroup"
      @on-group-select="onGroupSelect"
    ></GroupList>
    <ScriptList
      v-if="suggestionsByQA.commonDataList.length > 0"
      :dataList="headleDataList()"
      @send="
        (msg) => {
          if (!isQAItem(msg)) return;
          emitter.emit('onPickMsgAction', {
            msgType: 11,
            msgContent: { text: msg.answer },
          });
          terminal === 1 && router.back();
        }
      "
    >
      <template #title="{ item }">
        <div
          v-if="isQAItem(item)"
          class="whitespace-nowrap overflow-hidden overflow-ellipsis w-170"
        >
          {{ item.question }}
        </div>
      </template>
      <template #default="{ item }">
        <div v-if="isQAItem(item)" class="text-12 mt-10">
          <span>{{ item.answer }}</span>
        </div>
      </template>
    </ScriptList>
    <div
      class="h-150 flex flex-col justify-center items-center"
      v-else
      :class="{
        'text-12': !terminal,
        'text-[.5rem]': terminal,
      }"
    >
      暂无
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, onMounted, inject } from "vue";
import { getQASuggestions, QAItem } from "@/api/chat";
import { uniq } from "lodash";
import { EventEmitter } from "@/utils/eventEmitter";
import { useRouter } from "vue-router";
import GroupList from "./Common/GroupList.vue";
import { CategoryList } from "@/api/instruct";
import ScriptList from "./ScriptList.vue";
import { isQAItem } from "./Common/type";

const terminal = inject("terminal");
const emitter = inject<EventEmitter>("emitter")!;
const router = useRouter();
const suggestionsByQA = reactive<{
  selectedGroup: string;
  groupList: CategoryList["itemList"];
  commonDataList: (QAItem & { id: number })[];
}>({
  selectedGroup: "默认分组",
  groupList: [],
  commonDataList: [],
});

const headleDataList = () => {
  const categoryName = suggestionsByQA.groupList.find(
    (i) => i.categoryType === suggestionsByQA.selectedGroup,
  )!.categoryName;
  return suggestionsByQA.commonDataList.filter((item) => {
    if (categoryName === "默认分组") {
      return item;
    } else {
      return item.category === categoryName;
    }
  });
};

onMounted(async () => {
  const res = await getQASuggestions({});
  if (!res.itemList.length) {
    return;
  }
  suggestionsByQA.commonDataList = res.itemList.map((item, index) => {
    return {
      id: index + 1,
      ...item,
    };
  });
  suggestionsByQA.groupList = uniq(
    res.itemList.map((item) => item.category || "默认分组"),
  ).map((item, index) => {
    return {
      categoryName: item,
      categoryType: (index + 1).toString(),
    };
  });
  suggestionsByQA.selectedGroup = suggestionsByQA.groupList[0].categoryType;
});

const onGroupSelect = (item: string) => {
  suggestionsByQA.selectedGroup = item;
};
</script>

<style lang="less" scoped>
.title {
  padding: 5px 12px;
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  margin-top: 10px;
  border-bottom: 1px solid #eee;

  .label {
    border: 1px solid rgb(209, 213, 219);
    border-radius: 5px;
    padding: 0 5px;
    margin: 0 5px 5px 0;
    font-size: 0.3rem;
  }
}

.content {
  border-bottom: 1px solid #eee;
  padding: 12px;
  font-size: 0.3rem;
  cursor: pointer;
}
</style>
