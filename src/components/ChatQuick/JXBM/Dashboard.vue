<template>
  <div
    class="mt-17 mb-12 font-semibold text-[rgba(51,51,51,0.85)]"
    :class="{
      'text-15': !terminal,
      'text-[.32rem]': terminal,
    }"
  >
    基础信息
  </div>
  <div
    class="border-1 border-solid border-[#e6eef4]"
    :class="{
      'text-15/39': !terminal,
      'text-[.32rem]/39': terminal,
    }"
  >
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] rounded-tl-5 border-1 border-solid border-[#e6eef4]"
      >
        木仓昵称
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white rounded-tr-5 border-1 border-solid border-[#e6eef4] [border-left:0]"
      >
        {{ dashboard?.nickName }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        木仓ID
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-top:0] [border-left:0]"
        v-if="dashboard?.mucangId"
      >
        <div class="flex-1 whitespace-nowrap overflow-hidden text-ellipsis">
          {{ dashboard.mucangId }}
        </div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="copyMucangId(dashboard?.mucangId)"
          v-if="dashboard.mucangId"
        >
          复制
        </div>
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-top:0] [border-left:0]"
        v-else
      >
        <div
          class="flex flex-1 whitespace-nowrap overflow-hidden text-ellipsis"
        >
          <input
            type="text"
            v-model="phone"
            maxlength="11"
            placeholder="请输入手机号完成绑定"
          />
          <div
            class="flex-1 ml-10 cursor-pointer text-center"
            style="color: #04a5ff"
            @click="bindPhone"
          >
            绑定
          </div>
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        车型
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.carType && CarTypeMapper[dashboard.carType] }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        城市
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.cityName }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        手机号
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div>{{ dashboard?.phoneMask }}</div>
        <span
          class="w-60 cursor-pointer"
          style="color: #04a5ff"
          @click="showUserPhone"
          v-if="dashboard?.phoneMask"
          >显示</span
        >
        <span
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="onCallClick"
          v-if="dashboard?.phoneMask"
          >呼叫</span
        >
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        姓名
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div class="flex-1">{{ dashboard?.name || "--" }}</div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        职业
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div class="flex-1">{{ dashboard?.work || "--" }}</div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="editField('work', '职业')"
        >
          编辑
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        年龄
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div class="flex-1">{{ dashboard?.age || "--" }}</div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="editField('age', '年龄')"
        >
          编辑
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        性别
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white flex border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div class="flex-1">
          {{
            dashboard?.gender !== undefined
              ? GenderMapper[dashboard.gender]
              : "--"
          }}
        </div>
        <div
          class="w-60 ml-10 cursor-pointer"
          style="color: #04a5ff"
          @click="editGender"
        >
          编辑
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        VIP类型
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.vipTypes }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        做题数
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.totalExercisesCount || "--" }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        模考次数
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.mockTimes || "--" }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        近五次模考平均分
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        {{ dashboard?.latestFiveTimeAvgScore || "--" }}
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0]"
      >
        跟进人
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0]"
      >
        <div v-if="dashboard?.followUserName">
          {{ dashboard?.followUserName }}
        </div>
        <div v-else>
          <span>暂无</span
          ><span
            class="text-[#04a5ff] ml-10 cursor-pointer"
            @click="onFollowUserClick"
            >我来跟进</span
          >
        </div>
      </div>
    </div>
    <div class="flex">
      <div
        class="pl-15 w-100 text-left bg-[#f6f9fb] border-1 border-solid border-[#e6eef4] [border-top:0] rounded-bl-5"
      >
        学车地图
      </div>
      <div
        class="w-0 flex-1 pl-14 bg-white border-1 border-solid border-[#e6eef4] [border-left:0] [border-top:0] rounded-br-5"
      >
        <span
          class="text-[#04a5ff] cursor-pointer"
          @click="
            aMapPopUpCompRef?.open(
              session!.sessionKey,
              dashboard?.latitude,
              dashboard?.longitude,
              dashboard?.mucangId,
            )
          "
          >查看</span
        >
      </div>
    </div>
  </div>

  <!-- 客户标签 -->
  <div class="mt-16" v-if="dashboard?.leadNo">
    <div
      class="mb-8 font-semibold text-[rgba(51,51,51,0.85)] flex align-center justify-between text-13"
    >
      <span class="text-15 font-bold">客户标签</span>
      <CustomerTags :lead-no="dashboard.leadNo" />
    </div>
    <div class="bg-white rounded-8 p-16">
      <div class="mb-16">
        <div v-if="selectedTags.length === 0" class="text-gray-500">
          暂无选中标签
        </div>
        <div v-else class="flex flex-wrap gap-8">
          <div
            v-for="(tag, index) in selectedTags"
            :key="index"
            class="px-8 py-6 rounded-8 bg-[#e6f2fb] text-14 text-[#04a5ff] shadow-sm flex items-center"
          >
            <span>{{ tag.name }}</span>
            <span
              class="ml-4 cursor-pointer text-gray-500 hover:text-red-500"
              @click="removeSelectedTag(tag)"
              >×</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 客户备注 -->
  <div class="mt-16" v-if="dashboard?.userNo">
    <div
      class="mb-8 font-semibold text-[rgba(51,51,51,0.85)] flex align-center justify-between"
    >
      <span class="text-15 font-bold">客户备注</span>
      <span
        class="text-[#04a5ff] cursor-pointer text-13"
        @click="showCreateRemark"
        >添加备注</span
      >
    </div>
    <div>
      <div
        class="bg-white rounded-8 p-10 mt-15"
        v-for="(item, index) in userRemarks"
        :key="index"
      >
        <div>
          <div class="flex justify-between align-center">
            <span class="text-[#666666] text-14 px-8 py-6">
              {{ item.createUserName }}
            </span>
            <span class="text-[#999999] text-12 px-8 py-6">
              {{ dateFormat(item.createTime, "yyyy-MM-dd HH:mm") }}
            </span>
          </div>
          <p class="text-[#333333] text-14 px-8 py-6 break-words">
            {{ item.remark }}
          </p>
        </div>
      </div>
    </div>
  </div>

  <Model v-model="showAddRemarkModal" bg="rgba(0,0,0,.3)">
    <div
      class="relative bg-white overflow-hidden text-16"
      :class="{
        'w-[500px] rounded-lg': !terminal,
        'w-[90vw] rounded-[5px]': terminal,
      }"
      style="line-height: 1.4"
    >
      <!-- Header with title and close button -->
      <div
        class="flex justify-between items-center border-b border-gray-200"
        :class="{
          'p-[20px]': !terminal,
          'p-[.3rem]': terminal,
        }"
      >
        <div
          class="font-bold"
          :class="{
            'text-[18px]': !terminal,
            'text-[.36rem]': terminal,
          }"
        >
          添加客户备注
        </div>
        <div
          class="cursor-pointer absolute"
          :class="{
            'right-[20px] top-[20px]': !terminal,
            'right-[.3rem] top-[.3rem]': terminal,
          }"
          @click="showAddRemarkModal = false"
        >
          <div
            :class="{
              'w-[24px] h-[24px]': !terminal,
              'w-[.48rem] h-[.48rem]': terminal,
            }"
            v-html="$icon_close"
          ></div>
        </div>
      </div>

      <!-- Content -->
      <div
        :class="{
          'p-[20px]': !terminal,
          'p-[.3rem]': terminal,
        }"
      >
        <div class="mb-10">
          <div class="mb-8 font-medium">备注内容：</div>
          <textarea
            v-model="newRemarkContent"
            :class="{
              'w-full p-8 border border-[#e6eef4] rounded-8 min-h-[120px]':
                !terminal,
              'w-full p-[.16rem] border border-[#e6eef4] rounded-[.16rem] min-h-[2.4rem]':
                terminal,
            }"
            placeholder="请输入备注内容"
          ></textarea>
        </div>
      </div>

      <!-- Footer with buttons -->
      <div
        class="flex justify-end border-t border-gray-200 space-x-[10px]"
        :class="{
          'p-[20px]': !terminal,
          'p-[.3rem]': terminal,
        }"
      >
        <button
          class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-100"
          :class="{
            'px-[15px] py-[8px] text-[14px] rounded-lg': !terminal,
            'px-[.3rem] py-[.16rem] text-[.28rem] rounded-[5px]': terminal,
          }"
          @click="showAddRemarkModal = false"
        >
          取消
        </button>
        <button
          class="bg-[#04a5ff] text-white hover:bg-blue-600"
          :class="{
            'px-[15px] py-[8px] text-[14px] rounded-lg': !terminal,
            'px-[.3rem] py-[.16rem] text-[.28rem] rounded-[5px]': terminal,
          }"
          :disabled="!newRemarkContent.trim() || addingRemark"
          @click="addUserRemark"
        >
          <span v-if="addingRemark">添加中...</span>
          <span v-else>确定</span>
        </button>
      </div>
    </div>
  </Model>

  <AMapPopUp ref="aMapPopUpCompRef"></AMapPopUp>
  <CallModel ref="callModelCompRef"></CallModel>
</template>

<script lang="ts" setup>
import { ref, inject, watch } from "vue";

import {
  MonaDashboard,
  queryUserPhone,
  salesFollowRecordBind,
  queryDashboard,
  updateUserInfo,
  bindPhoneStore,
  TagVO,
  getUserTags,
  getTagList,
  TagValueVO,
  UserRemarkItem,
  getUserRemarks,
  createUserRemark,
} from "@/api/jxbm";
import { makeToast, prompt } from "@/utils/dom";
import { CarTypeMapper } from "@/utils/helpers";
import Model from "@/components/Model.vue";
import CustomerTags from "./CustomerTags.vue";
import AMapPopUp from "@/components/Common/AMapPopUp/index.vue";
import CallModel from "@/components/ChatQuick/Common/CallModel.vue";
import { initWxWork } from "@/initialize/wwinit";
import { isWxwork } from "@/utils/platform";
import $icon_close from "@/assets/close.svg?raw";
import { dateFormat } from "@/utils/format";
const props = defineProps<{
  session?: {
    sessionKey: string;
  };
}>();
const emit = defineEmits<{
  (e: "update:modelValue", dashboard: MonaDashboard): void;
}>();

const dashboard = ref<MonaDashboard>();
const aMapPopUpCompRef = ref<InstanceType<typeof AMapPopUp>>();
const callModelCompRef = ref<InstanceType<typeof CallModel>>();
const terminal = inject("terminal") as 0 | 1;
const phone = ref();

const selectedTags = ref<TagVO[]>([]);
const userTags = ref<TagValueVO[]>([]);
const tagOptions = ref<TagVO[]>([]);

const loadingRemarks = ref(false);
const userRemarks = ref<UserRemarkItem[]>([]);
const showAddRemarkModal = ref(false);
const newRemarkContent = ref("");
const addingRemark = ref(false);

const GenderMapper: Record<number, string> = {
  0: "女",
  1: "男",
  99: "未知",
};

const bindPhone = async () => {
  const res = await bindPhoneStore({ phone: phone.value });
  if (res.value) {
    makeToast("绑定成功");
    fetchDashBoardInfo();
  } else {
    makeToast("绑定失败");
  }
};

const copyMucangId = (mucangId: string) => {
  try {
    navigator.clipboard.writeText(mucangId);
    makeToast("复制成功");
  } catch {
    makeToast("复制失败");
  }
};

const showUserPhone = async () => {
  const { value: phone } = await queryUserPhone({
    mucangId: dashboard.value!.mucangId,
  });
  prompt({
    title: "用户手机号",
    content: `<div style="font-size: 20px;">${phone}</div>`,
    terminal,
    showCancel: false,
  });
};

/**
 * 销售人跟进
 */
const onFollowUserClick = async () => {
  if (dashboard.value?.mucangId) {
    const { value } = await salesFollowRecordBind({
      leadNo: dashboard.value?.leadNo,
    });
    if (value) {
      makeToast("绑定成功");
    } else {
      makeToast("绑定失败");
    }
  }
};

/**
 * 呼叫用户
 */
const onCallClick = async () => {
  if (dashboard.value) {
    // 调用呼叫组件，传递完整的参数
    callModelCompRef.value?.open(
      props.session!.sessionKey, // 会话密钥
      dashboard.value.mucangId, // 用户ID
      dashboard.value.cityCode || "", // 城市编码，如果没有则使用空字符串
    );
  }
};

/**
 * 编辑字段
 */
const editField = async (field: "name" | "work" | "age", fieldName: string) => {
  if (!props.session?.sessionKey) return;

  const currentValue = dashboard.value?.[field] || "";
  const inputType = field === "age" ? "number" : "text";

  await prompt({
    title: `编辑${fieldName}`,
    content: `<input type="${inputType}" id="editInput" value="${currentValue}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px;" placeholder="请输入${fieldName}">`,
    terminal,
    showCancel: true,
  });

  const input = document.getElementById("editInput") as HTMLInputElement;
  const newValue = input?.value?.trim();

  if (newValue && newValue !== String(currentValue)) {
    try {
      const updateData: any = { sessionKey: props.session.sessionKey };
      if (field === "age") {
        updateData[field] = parseInt(newValue);
      } else {
        updateData[field] = newValue;
      }

      const { value } = await updateUserInfo(updateData);
      if (value) {
        makeToast(`${fieldName}更新成功`);
        fetchDashBoardInfo();
      } else {
        makeToast(`${fieldName}更新失败`);
      }
    } catch (error) {
      makeToast(`${fieldName}更新失败`);
    }
  }
};

/**
 * 编辑性别
 */
const editGender = async () => {
  if (!props.session?.sessionKey) return;

  const currentGender = dashboard.value?.gender;
  const genderOptions = Object.entries(GenderMapper).map(([value, label]) => ({
    value: Number(value),
    label,
  }));

  const optionsHtml = genderOptions
    .map(
      (option) =>
        `<label style="
          display: flex;
          align-items: center;
          margin: 12px 0;
          padding: 10px 16px;
          border-radius: 8px;
          cursor: pointer;
          transition: background-color 0.2s;
          border: 1px solid #e6e6e6;
        " onmouseover="this.style.backgroundColor='#f8f9fa'; this.style.borderColor='#d0d7de';" onmouseout="this.style.backgroundColor='transparent'; this.style.borderColor='#e6e6e6';">
          <input type="radio" name="gender" value="${option.value}" ${currentGender === option.value ? "checked" : ""} style="
            margin: 0 12px 0 0;
            transform: scale(1.3);
            accent-color: #04a5ff;
            width: 50%;
          ">
          <span style="font-size: 16px; color: #333; font-weight: 500;">${option.label}</span>
    </label>`,
    )
    .join("");

  const dom = await prompt({
    title: "编辑性别",
    content: `<div style="padding: 12px 0; min-width: 200px;">${optionsHtml}</div>`,
    terminal,
    showCancel: true,
  });

  const selectedRadio = dom.querySelector(
    'input[name="gender"]:checked',
  ) as HTMLInputElement;

  if (selectedRadio) {
    const newGender = parseInt(selectedRadio.value);

    if (newGender !== currentGender) {
      try {
        const { value } = await updateUserInfo({
          sessionKey: props.session.sessionKey,
          gender: newGender,
        });
        if (value) {
          makeToast("性别更新成功");
          fetchDashBoardInfo();
        } else {
          makeToast("性别更新失败");
        }
      } catch (error) {
        makeToast("性别更新失败");
      }
    }
  } else {
    makeToast("请选择性别");
  }
};

const fetchDashBoardInfo = async () => {
  if (isWxwork) {
    await initWxWork();
  }
  dashboard.value = await queryDashboard({
    sessionKey: props.session!.sessionKey,
  });
  // 需要同步v-model
  emit("update:modelValue", dashboard.value);
};

const removeSelectedTag = (tag: TagVO) => {
  const index = selectedTags.value.findIndex(
    (selectedTag) => selectedTag.code === tag.code,
  );
  if (index !== -1) {
    selectedTags.value.splice(index, 1);
  }
};

const showUserTags = async () => {
  const leadNo = dashboard.value?.leadNo;
  if (!leadNo) {
    makeToast("未获取到用户编号");
    return;
  }

  selectedTags.value = [];

  try {
    // 获取用户已有标签
    const userTagsResponse = await getUserTags({
      bizType: "lead",
      bizCode: leadNo,
    });

    if (userTagsResponse.tags) {
      userTags.value = userTagsResponse.tags;
    }

    // 获取所有可用标签
    const tagListResponse = await getTagList({
      limit: 100,
    });

    if (tagListResponse) {
      tagOptions.value = tagListResponse;

      // 设置已选标签
      if (userTags.value.length > 0) {
        userTags.value.forEach((userTag) => {
          const matchedTag = tagOptions.value.find(
            (tag) => tag.code === userTag.tagCode,
          );
          if (matchedTag) {
            selectedTags.value.push(matchedTag);
          }
        });
      }
    }
  } catch (error) {
    console.error("获取标签数据失败", error);
    makeToast("获取标签数据失败");
  } finally {
  }
};

const showCreateRemark = () => {
  showAddRemarkModal.value = true;
  newRemarkContent.value = "";
};

const showUserRemarks = async () => {
  if (!dashboard.value?.userNo) {
    makeToast("未获取到用户编号");
    return;
  }

  loadingRemarks.value = true;
  userRemarks.value = [];

  try {
    userRemarks.value = await getUserRemarks({
      userNo: dashboard.value.userNo,
    });
    console.log(userRemarks.value);
  } catch (error) {
    console.error("获取用户备注失败", error);
    makeToast("获取用户备注失败");
  } finally {
    loadingRemarks.value = false;
  }
};

const addUserRemark = async () => {
  if (!dashboard.value?.userNo) {
    makeToast("未获取到用户编号");
    return;
  }

  if (!newRemarkContent.value.trim()) {
    makeToast("请输入备注内容");
    return;
  }

  addingRemark.value = true;

  try {
    await createUserRemark({
      userNo: dashboard.value.userNo,
      remark: newRemarkContent.value.trim(),
    });

    makeToast("添加备注成功");
    showAddRemarkModal.value = false;

    // 刷新备注列表
    showUserRemarks();
  } catch (error) {
    console.error("添加用户备注失败", error);
    makeToast("添加备注失败");
  } finally {
    addingRemark.value = false;
  }
};

watch(
  () => props.session,
  async () => {
    await fetchDashBoardInfo();
    showUserTags();
    showUserRemarks();
  },
  { immediate: true },
);
</script>
