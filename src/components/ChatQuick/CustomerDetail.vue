<template>
  <div class="w-full h-full loadingGif">
    <iframe width="100%" height="100%" frameborder="0" :src="url"></iframe>
  </div>
</template>

<script setup lang="ts">
import { computed, inject } from "vue";
import { URLParams, isTest } from "@/utils/tools";
import { objToParams } from "@/utils/tools";
import { useSessionStore, useAccountStore } from "@/store/index";
import { storeToRefs } from "pinia";
import { useRoute } from "vue-router";
const props = defineProps<{
  type?: "userinfo" | "order" | "student-analysis" | "teach";
}>();
const route = useRoute();
const terminal = inject<number>("terminal");

const { session } = storeToRefs(useSessionStore());
const { account } = storeToRefs(useAccountStore());

const url = computed(() => {
  const { type } = props;
  const {
    customerMucangId: mucangId,
    customerWxUnionId: customerUnionId,
    staffSsoId: saleSsoId,
    customerCode: customerCode,
    sessionKey,
  } = session.value!;
  const urlPath = {
    userinfo: "jiakaobaodian-personal-training/customer-detail.html",
    order: "jiakaobaodian-personal-training/set-price.html",
    "student-analysis": "jiakaobaodian-share-vip/student-analysis.html",
    teach: "jiakaobaodian-share-vip/teach.html",
  }[terminal === 1 ? (route.params.type as string) : props.type!];
  const params =
    type === "userinfo"
      ? {
          mucangId,
          sessionKey,
          unionId: customerUnionId,
          saleSsoId,
        }
      : {
          mucangId,
          saleSsoId,
          customerUnionId,
          sessionKey,
          customerCode,
          accountId: account.value?.id,
        };
  const useReleaseDomain =
    !isTest ||
    (type === "userinfo" && URLParams.get("userInfoDomain") === "release");
  console.log(params);

  return `https://share-m.${useReleaseDomain ? "" : "ttt."}kakamobi.com/activity.kakamobi.com/${urlPath}?${objToParams(params)}`;
});
</script>

<style lang="less" scoped>
.none {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.4rem;
}
</style>
