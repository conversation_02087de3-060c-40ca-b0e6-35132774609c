import { QAItem, QuickMsgActionType } from "@/api/chat";
import { InstructionList } from "@/api/instruct";

export type ScriptListType =
  | InstructionList["itemList"][0]
  | QuickMsgActionType
  | (QAItem & { id: number });

export function isQuickMsgActionType(r: any): r is QuickMsgActionType {
  return !!(r && r.msgContent);
}

export function isInstructionList(r: any): r is InstructionList["itemList"][0] {
  return !!(r && r.instructionKey);
}

export function isQAItem(r: any): r is QAItem {
  return !!(r && r.question);
}
