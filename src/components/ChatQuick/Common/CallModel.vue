<template>
  <Model v-model="visible" bg="rgba(0,0,0,.3)">
    <div
      class="relative bg-white overflow-hidden"
      :class="{
        'w-[500px] rounded-lg': !terminal,
        'w-[90vw] rounded-[5px]': terminal,
      }"
    >
      <!-- Header with title and close button -->
      <div
        class="flex justify-between items-center border-b border-gray-200"
        :class="{
          'p-[20px]': !terminal,
          'p-[.3rem]': terminal,
        }"
      >
        <div
          class="font-bold"
          :class="{
            'text-[18px]': !terminal,
            'text-[.36rem]': terminal,
          }"
        >
          呼叫
        </div>
        <div
          class="cursor-pointer absolute"
          :class="{
            'right-[20px] top-[20px]': !terminal,
            'right-[.3rem] top-[.3rem]': terminal,
          }"
          @click="visible = false"
        >
          <div
            :class="{
              'w-[24px] h-[24px]': !terminal,
              'w-[.48rem] h-[.48rem]': terminal,
            }"
            v-html="$icon_close"
          ></div>
        </div>
      </div>

      <!-- Content -->
      <div
        :class="{
          'p-[20px]': !terminal,
          'p-[.2rem]': terminal,
        }"
      >
        <div
          :class="{
            'mb-[15px]': !terminal,
            'mb-[.2rem]': terminal,
          }"
        >
          <div
            :class="{
              'mb-[10px] text-[14px]': !terminal,
              'mb-[.1rem] text-[.28rem]': terminal,
            }"
          >
            呼出手机号：
          </div>
          <select
            v-model="selectedPhone"
            class="w-full border border-gray-300 focus:outline-none focus:ring-1 focus:ring-blue-500"
            :class="{
              'h-[40px] px-[10px] text-[14px] rounded-lg': !terminal,
              'h-[.8rem] px-[.2rem] text-[.28rem] rounded-[5px]': terminal,
            }"
          >
            <option value="" disabled>请选择呼出手机号</option>
            <option
              class="text-16"
              v-for="phone in phoneList"
              :key="phone.value"
              :value="phone.value"
            >
              {{ phone.label }}
            </option>
          </select>
          <div
            class="text-pink-500"
            :class="{
              'mt-[10px] text-[14px]': !terminal,
              'mt-[.2rem] text-[.28rem]': terminal,
            }"
            v-if="phoneList.length === 0"
          >
            <div
              class="border border-dashed border-pink-500 rounded-[5px]"
              :class="{
                'p-[10px]': !terminal,
                'p-[.2rem]': terminal,
              }"
            >
              option List写死, 销后询问销售后提供
            </div>
          </div>
        </div>
      </div>

      <!-- Footer with buttons -->
      <div
        class="flex justify-end border-t border-gray-200 space-x-[10px]"
        :class="{
          'p-[20px]': !terminal,
          'p-[.3rem]': terminal,
        }"
      >
        <button
          class="bg-white border border-gray-300 text-gray-700 hover:bg-gray-100"
          :class="{
            'px-[15px] py-[8px] text-[14px] rounded-lg': !terminal,
            'px-[.3rem] py-[.16rem] text-[.28rem] rounded-[5px]': terminal,
          }"
          @click="visible = false"
        >
          取消
        </button>
        <button
          class="bg-[#4096ff] text-white hover:bg-blue-600"
          :class="{
            'px-[15px] py-[8px] text-[14px] rounded-lg': !terminal,
            'px-[.3rem] py-[.16rem] text-[.28rem] rounded-[5px]': terminal,
          }"
          :disabled="!selectedPhone || loading"
          @click="confirmCall"
        >
          <span v-if="loading">加载中...</span>
          <span v-else>确定</span>
        </button>
      </div>
    </div>

    <!-- 确认拨打弹窗 -->
    <div
      v-if="showConfirm"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[1000000]"
    >
      <div
        class="bg-white"
        :class="{
          'p-[20px] w-[500px] rounded-lg': !terminal,
          'p-[.3rem] w-[90vw] rounded-[5px]': terminal,
        }"
      >
        <div class="relative">
          <div
            class="font-bold"
            :class="{
              'text-[18px] mb-[20px]': !terminal,
              'text-[.36rem] mb-[.3rem]': terminal,
            }"
          >
            请拨打
          </div>
          <div
            class="absolute cursor-pointer right-0 top-0"
            @click="showConfirm = false"
          >
            <div
              :class="{
                'w-[24px] h-[24px]': !terminal,
                'w-[.48rem] h-[.48rem]': terminal,
              }"
              v-html="$icon_close"
            ></div>
          </div>
        </div>
        <div
          class="text-center font-bold"
          :class="{
            'text-[24px] mb-[20px]': !terminal,
            'text-[.48rem] mb-[.3rem]': terminal,
          }"
        >
          {{ formattedPrivacyPhone }}
        </div>
        <div
          class="text-[#ff7d00] text-center"
          :class="{
            'text-[14px] mb-[20px]': !terminal,
            'text-[.28rem] mb-[.3rem]': terminal,
          }"
        >
          仅1分钟有效，请尽快拨打
        </div>
        <div class="flex justify-center">
          <button
            class="bg-[#4096ff] text-white hover:bg-blue-600"
            :class="{
              'px-[15px] py-[8px] text-[14px] rounded-lg': !terminal,
              'px-[.3rem] py-[.16rem] text-[.28rem] rounded-[5px]': terminal,
            }"
            @click="callComplete"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </Model>
</template>

<script setup lang="ts">
import { ref, defineExpose, inject, computed, onMounted } from "vue";
import Model from "@/components/Model.vue";
// 导入关闭图标SVG，使用?raw后缀以便可以通过v-html指令直接渲染SVG内容
import $icon_close from "@/assets/close.svg?raw";
import { queryPrivacyPhone } from "@/api/jxbm";
import { makeToast } from "@/utils/dom";
import { getSwallowConfig } from "@/api/chores";

// 注入terminal变量用于判断是否为移动端，0表示PC端，1表示移动端
const terminal = inject("terminal") as 0 | 1;

// 控制弹窗显示状态
const visible = ref(false);
// 控制确认拨打弹窗显示状态
const showConfirm = ref(false);
// 存储用户选择的呼出手机号
const selectedPhone = ref("");
// 存储会话密钥
const sessionKey = ref("");
// 存储用户ID
const userId = ref("");
// 存储城市编码
const cityCode = ref("");
// 加载状态
const loading = ref(false);
// 存储从接口返回的隐私号码
const privacyPhone = ref("");

// 呼出手机号列表数据（前端写死的固定号码）
// 每个选项包含label（显示文本）和value（实际值）
const phoneList = ref<Array<{ label: string; value: string }>>([]);
onMounted(async () => {
  const res = (await getSwallowConfig({
    key: "jk_fxx_call_phone",
  })) as string[];
  phoneList.value = res.map((item) => ({ label: item, value: item }));
});

/**
 * 格式化隐私号码以便于显示
 * 将11位手机号格式化为"XXX XXXX XXXX"的形式
 */
const formattedPrivacyPhone = computed(() => {
  if (!privacyPhone.value) return "";

  const phone = privacyPhone.value;
  if (phone.length === 11) {
    return `${phone.substring(0, 3)} ${phone.substring(3, 7)} ${phone.substring(7)}`;
  } else {
    return phone;
  }
});

/**
 * 打开呼叫弹窗
 * @param session 会话密钥
 * @param user 用户ID
 * @param city 城市编码
 */
const open = (session: string, user: string, city: string) => {
  sessionKey.value = session;
  userId.value = user;
  cityCode.value = city;
  visible.value = true;
  showConfirm.value = false;
  selectedPhone.value = "";
  privacyPhone.value = "";
  loading.value = false;
};

/**
 * 确认呼叫，发起请求并显示确认拨打弹窗
 * 当用户选择了呼出手机号后才能确认呼叫
 */
const confirmCall = async () => {
  if (selectedPhone.value) {
    try {
      loading.value = true;
      // 发起请求获取隐私号码
      // 使用用户选择的呼出手机号（selectedPhone.value）
      const response = await queryPrivacyPhone({
        sessionKey: sessionKey.value,
        userId: userId.value,
        cityCode: cityCode.value,
        phone: selectedPhone.value,
      });

      // 从响应中获取隐私号码
      if (response && response.privacyPhone) {
        privacyPhone.value = response.privacyPhone;
        // 请求成功，显示确认拨打弹窗
        showConfirm.value = true;
      } else {
        makeToast("获取隐私号码失败，请重试");
      }
    } catch (error) {
      // 请求失败，显示错误提示
      makeToast("获取隐私号码失败，请重试");
      console.error("Error querying privacy phone:", error);
    } finally {
      loading.value = false;
    }
  }
};

/**
 * 完成呼叫，关闭所有弹窗
 * 用户点击"拨打完成"按钮后调用
 */
const callComplete = () => {
  showConfirm.value = false;
  visible.value = false;
};

// 对外暴露open方法，允许父组件调用此方法打开呼叫弹窗
defineExpose({
  /**
   * 打开呼叫弹窗
   * @param phone 不再使用，保留参数以保持兼容性
   * @param session 会话密钥
   * @param user 用户ID
   * @param city 城市编码
   */
  open,
});
</script>

<style scoped></style>
