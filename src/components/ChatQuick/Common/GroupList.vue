<template>
  <div>
    <div class="group-title text-12 font-bold pt-17 text-[#464646]">
      {{ title }}
    </div>
    <div v-if="groupDataList && groupDataList?.length > 0" class="mt-14">
      <div class="group text-14/20">
        <div class="flex w-full items-center flex-wrap">
          <div
            class="cursor-pointer flex-[1_auto_auto] whitespace-nowrap border-1 border-solid py-5 px-10 rounded-5 m-5"
            :class="{
              'border-[#04a5ff] bg-[#c7eaff] text-[#04a5ff]':
                item.categoryType === categoryType,
              'text-[#6E6E6E] border-[#CCCFD0]':
                item.categoryType !== categoryType,
            }"
            v-for="item in groupDataList"
            :key="item.categoryType"
            @click="categoryType = item.categoryType"
          >
            {{ item.categoryName }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CategoryList } from "@/api/instruct";
import { computed } from "vue";
const props = defineProps<{
  title: string;
  groupDataList: CategoryList["itemList"];
  selectGroup: string;
}>();

const emit = defineEmits<{
  (e: "onGroupSelect", item: string): void;
}>();

const categoryType = computed({
  get: () => props.selectGroup,
  set: (value: string) => {
    emit("onGroupSelect", value);
  },
});
</script>

<style scoped lang="less"></style>
