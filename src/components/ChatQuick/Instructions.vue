<template>
  <div
    class="flex flex-col px-13 py-11"
    :class="terminal ? 'overflow-auto h-screen' : ''"
  >
    <Search
      :disable="headleDisable"
      :onPreviewFn="headlePreview"
      :headle-search="headleSearch"
      @send="
        (msg) => {
          emitter.emit('onPickMsgAction', msg);
          terminal === 1 && router.back();
        }
      "
    >
      <template #title="{ item }" class="text-13">
        <span class="text-[#FF822D] text-13"
          >[{{
            isQuickMsgActionType(item) &&
            headlePrefix(item.msgType as InstructionsType)
          }}]</span
        >
        <span v-html="isQuickMsgActionType(item) && item.title"></span>
      </template>
      <template #default="{ item }">
        <InstructionsItem
          :msg="isQuickMsgActionType(item) ? item : ''"
        ></InstructionsItem>
      </template>
    </Search>
    <GroupList
      title="资源分组"
      :group-data-list="groupListRef"
      :select-group="selectGroupRef"
      @on-group-select="onGroupSelect"
    >
    </GroupList>
    <ScriptList
      v-if="suggestionsQK"
      :disable="headleDisable"
      :onPreviewFn="headlePreview"
      @send="
        (msg) => {
          emitter.emit('onPickMsgAction', msg, true);
          terminal === 1 && router.back();
        }
      "
      :dataList="headleQK()"
    >
      <template #title="{ item }" class="text-13">
        <span class="text-[#FF822D] text-13"
          >[{{
            isQuickMsgActionType(item) &&
            headlePrefix(item.msgType as InstructionsType)
          }}]</span
        >
        <span v-html="isQuickMsgActionType(item) && item.title"></span>
      </template>
      <template #default="{ item }">
        <InstructionsItem
          :msg="isQuickMsgActionType(item) ? item : ''"
        ></InstructionsItem>
      </template>
    </ScriptList>
    <Loading
      :is-loading="isLoadingRef"
      :loading-text="loadingTextRef"
    ></Loading>
    <Model v-if="modelVisibleRef" v-model="modelVisibleRef" bg="rgba(0,0,0,.7)">
      <div class="flex flex-col justify-center items-center">
        <img
          :src="modelUrlRef"
          alt=""
          class="object-contain"
          v-if="modelTypeRef === 'image'"
        />
        <video :src="modelUrlRef" controls v-else></video>
      </div>
    </Model>
  </div>
</template>

<script lang="ts" setup>
import {
  getQuickInstructions,
  QuickMsgActionType,
  MsgAction,
} from "@/api/chat";
import Search from "./Common/Search.vue";
import { EventEmitter } from "@/utils/eventEmitter";
import { ref, onMounted, inject } from "vue";
import { useRouter } from "vue-router";
import Loading from "../Loading.vue";
import { instructionsType } from "@/utils/helpers";
import InstructionsItem from "./instructionsItem.vue";
import GroupList from "./Common/GroupList.vue";
import { CategoryList } from "@/api/instruct";
import { HostNames } from "@/utils/request";
import ScriptList from "./ScriptList.vue";
import Model from "../Model.vue";
import { ScriptListType, isQuickMsgActionType } from "./Common/type";

type InstructionsType = keyof typeof instructionsType;

const router = useRouter();
const emitter = inject<EventEmitter>("emitter")!;
const terminal = inject<number>("terminal");
const isLoadingRef = ref<boolean>(false);
const loadingTextRef = ref<string>("");
const groupListRef = ref<CategoryList["itemList"]>([]); // 分组列表
const selectGroupRef = ref<string>("");
const modelVisibleRef = ref(false);
const modelUrlRef = ref("");
const modelTypeRef = ref<"video" | "image">("image");
// 远程数据
const suggestionsQK = ref<QuickMsgActionType[]>([]);

onMounted(async () => {
  const res = await getQuickInstructions({});
  suggestionsQK.value = res.itemList.map((item, index) => {
    return {
      ...item,
      title: headleTitle(item),
      id: index + 1,
    };
  });
  let group: string[] = ["默认分组"];
  for (let key in res.itemList) {
    if (res.itemList[key].labelList) {
      group = [...new Set([...group, ...res.itemList[key].labelList])];
    }
  }
  groupListRef.value = [...group].map((item, index) => {
    return {
      categoryName: item,
      categoryType: (index + 1).toString(),
    };
  });
  selectGroupRef.value = groupListRef.value[0].categoryType;
});

const headleTitle = (item: MsgAction): string => {
  if (item.name) {
    return item.name;
  }
  return "";
};

const headlePrefix = (msgType: InstructionsType) => {
  return instructionsType[msgType];
};

const headleQK = () => {
  const tab =
    groupListRef.value.find((it) => it.categoryType === selectGroupRef.value)
      ?.categoryName || "";
  if (tab === "默认分组") {
    return suggestionsQK.value.filter((item) => item.labelList?.length === 0);
  } else {
    return suggestionsQK.value.filter((it) => it.labelList?.includes(tab));
  }
};
/**
 * 处理搜索功能
 * @param inputValue 输入框的值
 * @returns 返回处理好的值
 */
const headleSearch = (inputValue: string) => {
  isLoadingRef.value = true;
  loadingTextRef.value = "搜索中";
  const reg = new RegExp(inputValue, "ig");
  const data = suggestionsQK.value
    .map((item) => {
      const title = headleTitle(item);
      if (!title.includes(inputValue)) {
        return;
      }
      return {
        ...item,
        title: title.replace(
          reg,
          `<span class="text-[#04A5FF] text-13">${inputValue}</span>`,
        ),
        msgContent: {
          ...item.msgContent,
        },
      };
    })
    .filter((item) => {
      return item;
    });
  isLoadingRef.value = false;
  return data;
};
/**
 * 处理禁用的逻辑
 * @param item
 */
const headleDisable = (item: ScriptListType) => {
  if (isQuickMsgActionType(item)) {
    if (
      item.msgType === 112 ||
      item.msgType === 116 ||
      item.msgType === 117 ||
      item.msgType === 114 ||
      item.msgType === 10003 ||
      item.msgType === 119
    ) {
      return false;
    } else {
      return true;
    }
  }
  return true;
};

/**
 * 处理预览逻辑
 */
const headlePreview = (item: ScriptListType) => {
  if (isQuickMsgActionType(item)) {
    const AppWinObjFn = headleAppWinView(item);
    if (terminal) {
      // APP
      AppWinObjFn.App[
        item.msgType as Extract<InstructionsType, 111 | 113 | 115 | 11>
      ]();
    } else {
      // pc
      AppWinObjFn.Win[
        item.msgType as Extract<InstructionsType, 111 | 113 | 115 | 11>
      ]();
    }
  }
};

/**
 * 根据不同的情况进行预览
 * @param item
 * @returns 返回不同情况处理的对象
 */
const headleAppWinView = (item: QuickMsgActionType) => {
  const common = () => {
    return {
      115: () => {},
      11: () => {},
    };
  };
  return {
    App: {
      ...common(),
      111: () => {
        if (item.msgType === 111) {
          router.push({
            name: "WebPage",
            query: {
              srcdoc: `<img style="width: 100%; height: 100%; object-fit: fill;" src=${HostNames.chat + "/api/h5/media/get.htm?encodedData=" + item.msgContent.encodedData}/>`,
              title: "图片",
            },
          });
        }
      },
      113: () => {
        if (item.msgType === 113) {
          router.push({
            name: "WebPage",
            query: {
              srcdoc: `<video style="width: 100%; height: 100%; " src=${HostNames.chat + "/api/h5/media/get.htm?encodedData=" + item.msgContent.encodedData} controls>`,
              title: "视频",
            },
          });
        }
      },
    },
    Win: {
      ...common(),
      111: () => {
        if (item.msgType === 111) {
          modelTypeRef.value = "image";
          modelUrlRef.value =
            HostNames.chat +
            "/api/h5/media/get.htm?encodedData=" +
            item.msgContent.encodedData;
          modelVisibleRef.value = true;
        }
      },
      113: () => {
        if (item.msgType === 113) {
          modelTypeRef.value = "video";
          modelUrlRef.value =
            HostNames.chat +
            "/api/h5/media/get.htm?encodedData=" +
            item.msgContent.encodedData;
          modelVisibleRef.value = true;
        }
      },
    },
  };
};

const onGroupSelect = (item: string) => {
  selectGroupRef.value = item;
};
</script>

<style lang="less" scoped>
.swiper::-webkit-scrollbar {
  display: none;
}
</style>
