<template>
  <div class="AiContainer">
    <div class="flex items-center py-8 px-10 bg-gray-200" v-if="!terminal">
      <div class="font-bold">AI快捷回复</div>
      <div
        class="ml-10 w-18 h-18 cursor-pointer"
        v-html="$icon_refresh"
        @click="generateAISuggestions()"
      ></div>
    </div>
    <div
      :class="
        terminal
          ? 'information'
          : 'm-10 rounded-5 px-8 bg-gray-200 whitespace-nowrap overflow-hidden text-ellipsis'
      "
      v-if="msgList.content"
    >
      {{ msgList.content }}
    </div>
    <div
      :class="
        terminal
          ? 'AiButton'
          : 'w-150 text-16/36 rounded-4 text-center mx-auto my-30 bg-gray-300 cursor-pointer'
      "
      v-if="!suggestionsByAI"
      @click="generateAISuggestions()"
    >
      点击生成AI回复
    </div>
    <div
      :class="terminal ? 'none' : 'text-center my-30 opacity-50'"
      v-else-if="!suggestionsByAI.length"
    >
      暂无可用回复
    </div>
    <div
      :class="
        terminal
          ? 'break-words whitespace-pre-wrap cursor-pointer content'
          : 'border-b-1 border-b-[#eee] px-10 py-8 break-words whitespace-pre-wrap cursor-pointer flex items-start'
      "
      v-for="(msg, index) in suggestionsByAI"
      :key="index"
      @click="
        () => {
          emitter.emit('onPickMsgAction', msg);
          terminal === 1 && router.back();
        }
      "
      v-else
    >
      <div
        class="shrink-0 w-20 h-20 mt-2 mr-8 flex"
        v-html="$icon_backArrow"
      ></div>
      <MsgItem :msg="msg" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { getAISuggestions, MsgAction } from "@/api/chat";
import MsgItem from "@/components/MsgItem.vue";
import { inject, ref, watch } from "vue";
import $icon_refresh from "@/assets/refresh.svg?raw";
import $icon_backArrow from "@/assets/backArrow.svg?raw";
import { EventEmitter } from "@/utils/eventEmitter";
import { useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { useParamStore } from "@/store/index";

const props = defineProps<{
  msgList: {
    id: string;
    content: string;
  };
}>();

const msgList = ref<{
  id: string;
  content: string;
}>({
  id: "",
  content: "",
});
const router = useRouter();
const emitter = inject<EventEmitter>("emitter")!;
const terminal = inject("terminal");
const suggestionsByAI = ref<MsgAction[] | null>(null);
const generating = ref(false);

const generateAISuggestions = async () => {
  if (!msgList.value.id || generating.value) {
    suggestionsByAI.value = null;
    return;
  }
  generating.value = true;
  const res = await getAISuggestions({
    referenceMsgId: msgList.value.id,
  }).finally(() => {
    generating.value = false;
  });
  suggestionsByAI.value = res.recommendContents;
};

const setMsgList = (referenceMsg: { id: string; content: string }) => {
  msgList.value.id = referenceMsg.id;
  msgList.value.content = referenceMsg.content;
};
if (terminal) {
  const { param } = storeToRefs(useParamStore());
  setMsgList(param.value);
} else {
  watch(
    () => props.msgList.id,
    () => setMsgList(props.msgList),
    { immediate: true },
  );
}
watch(() => msgList.value.id, generateAISuggestions, { immediate: true });

defineExpose({
  generateAISuggestions,
});
</script>
<style lang="less" scoped>
.information {
  padding: 5px 10px;
  background-color: rgb(229, 231, 235);
  border-radius: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.3rem;
  margin: 10px;
}

.AiButton {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 0.15rem;
  border-radius: 5px;
  background-color: rgb(209, 213, 219);
  font-size: 0.3rem;
}
.none {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 0.15rem;
  font-size: 0.3rem;
}
.content {
  display: flex;
  border-bottom: 1px solid #eee;
  font-size: 0.3rem;
}
</style>
