<template>
  <div class="w-full h-[5rem] flex flex-col overflow-hidden">
    <Banner
      :list="banner"
      :duration="duration"
      :containerWidth="w"
      tabHeight="1rem"
      ref="bannerRef"
    >
      <template #default="{ item }">
        <div
          v-for="(imgUrl, text) in item.content"
          :key="text"
          :style="item.style"
          @click="item.onclick(text, imgUrl)"
        >
          <img :src="imgUrl" />
        </div>
      </template>
    </Banner>
  </div>
</template>

<script setup lang="ts">
import Banner from "./Banner/index.vue";
import { singleEmojiMapper, emojiMapper } from "./emoji/mapper";
import { computed, ref } from "vue";
import { useIconBut } from "./index";
import { MsgAction } from "@/api/chat";
const emit = defineEmits<{
  (e: "sendMessage", msg: Promise<MsgAction>): void;
  (e: "insertEmoji", emoji: { text: string; imgUrl: string }): void;
}>();

const { onSendSingleEmoji } = useIconBut({}, emit);

const banner = [
  {
    tab: "emoji",
    content: emojiMapper,

    style: {
      width: "1rem",
      padding: ".09rem",
      margin: "0 .12rem",
    },
    onclick: (text: string, imgUrl: string) => {
      emit("insertEmoji", { text, imgUrl });
    },
  },
  {
    tab: "典典表情包",
    content: singleEmojiMapper,
    style: {
      width: "1.6rem",
      padding: ".09rem",
      margin: "0 .12rem",
    },
    onclick: (text: string) => {
      onSendSingleEmoji(text);
    },
  },
];
const duration = 300;
const w = computed(() => document.body.clientWidth);
const bannerRef = ref();
</script>

<style scoped lang="less"></style>
