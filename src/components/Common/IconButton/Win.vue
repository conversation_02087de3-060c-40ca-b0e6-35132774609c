<template>
  <div class="flex gap-x-26">
    <Picker
      @pick-emoji="(emoji) => emit('insertEmoji', emoji)"
      @pick-single-emoji="onSendSingleEmoji"
      v-if="!isJSZP"
    >
      <div
        title="发送表情包"
        class="w-26 h-26 text-[#8C939D]"
        v-html="$body_emoji"
      ></div>
    </Picker>
    <label title="发送图片">
      <div v-html="$body_pic" class="text-[#8C939D] w-26 h-26"></div>
      <input
        class="block w-0 h-0"
        type="file"
        accept="image/*"
        @change="onSelectImage"
      />
    </label>
    <label title="发送视频">
      <div v-html="$body_video" class="text-[#8C939D] w-26 h-26"></div>
      <input
        class="block w-0 h-0"
        type="file"
        accept="video/*"
        @change="onSelectVideo"
      />
    </label>
  </div>
</template>

<script setup lang="ts">
import { MsgAction } from "@/api/chat";
import { useIconBut } from "./index";
import Picker from "./emoji/Picker.vue";
import { isJSZP } from "@/utils/helpers";

const emit = defineEmits<{
  (e: "sendMessage", msg: Promise<MsgAction>): void;
  (e: "insertEmoji", emoji: { text: string; imgUrl: string }): void;
}>();

const {
  $body_emoji,
  $body_pic,
  $body_video,
  onSelectImage,
  onSelectVideo,
  onSendSingleEmoji,
} = useIconBut({}, emit);
</script>

<style scoped></style>
