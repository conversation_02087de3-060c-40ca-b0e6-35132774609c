import { MsgAction } from "@/api/chat";
import $body_emoji from "@/assets/body_emoji.svg?raw";
import $body_pic from "@/assets/body_pic.svg?raw";
import $body_video from "@/assets/body_video.svg?raw";
// @ts-ignore
import MCSUpload from "@simplex/simple-secure-upload";

import { Store } from "@simplex/simple-base-sso";

// 库做了缓存，不要
sessionStorage.removeItem("MCSINITADMIN");

Store.extend({
  apiCors: false,
  autoLogin: true,
})
  .create({
    url: "https://cyclops.mucang.cn/api/admin/upload-file/upload.htm?from=mcs-pre-request",
  })
  .request();
interface Emit {
  (e: "sendMessage", msg: Promise<MsgAction>): void;
}

export function useIconBut(_props: {}, emit: Emit) {
  const onSelectImage = getUserSelectFile(emit, async (file) => {
    const encodedData = await uploadSingleFile(file);
    return {
      msgType: 13,
      msgContent: {
        name: file.name,
        encodedData,
      },
    };
  });

  const onSelectVideo = getUserSelectFile(emit, async (file) => {
    const video = getVideoFromFile(file);
    const [encodedData, snapshotEncodeData] = await Promise.all([
      uploadSingleFile(file),
      getVideoCover(video).then((data) => {
        return uploadSingleFile({ fileName: file.name, data });
      }),
    ]);
    return {
      msgType: 15,
      msgContent: {
        name: file.name,
        encodedData,
        snapshotEncodeData,
        duration: video.duration,
      },
    };
  });

  return {
    $body_emoji,
    $body_pic,
    $body_video,
    onSelectImage,
    onSelectVideo,
    onSendSingleEmoji: (text: string) => {
      emit(
        "sendMessage",
        Promise.resolve({
          msgType: 10006,
          msgContent: {
            text,
          },
        }),
      );
    },
    onSelectAlbum: async (e: Event) => {
      const input = e.target as HTMLInputElement;
      const files = input.files;
      if (!files || !files.length) {
        return;
      }
      if (files[0].type.includes("image/")) {
        console.log("图片");
        onSelectImage(e);
      } else if (files[0].type.includes("video/")) {
        console.log("视频");
        onSelectVideo(e);
      } else {
        input.value = "";
      }
    },
  };
}

function getUserSelectFile(
  emit: Function,
  getMessage: (file: File) => Promise<MsgAction>,
) {
  return async function onInputChange(e: Event) {
    const input = e.target as HTMLInputElement;
    const files = input.files;

    if (!files || !files.length) {
      return;
    }
    // 设置文件数据
    const file = files[0];
    input.value = "";
    console.log("进入了视频");

    emit("sendMessage", getMessage(file));
  };
}

/** 上传文件，返回encodedData */
function uploadSingleFile(
  file: File | { fileName: string; data: string },
): Promise<string> {
  return (
    new MCSUpload({
      admin: true,
      appSpaceId: "23f5443423f5341c1dc8",
      // @ts-ignore
      files: [file],
      chunkSize: 1.5,
      useMd5: false,
    })
      // @ts-ignore
      .then((sRes: any) => {
        console.log("成功", sRes);
        return sRes[0].encodedData;
      })
      .catch((fRes: any) => {
        console.log("失败", fRes);
      })
  );
}

function getVideoFromFile(file: File) {
  const url = URL.createObjectURL(file);
  const video = document.createElement("video");
  video.src = url;
  return video;
}

/** 获取视频文件的封面信息，base64格式的图片 */
function getVideoCover(video: HTMLVideoElement) {
  let promiseResolve: any;
  const promise = new Promise<string>((resolve) => {
    promiseResolve = resolve;
  });

  video.addEventListener("canplay", function canplay() {
    video.removeEventListener("canplay", canplay);
    // 设置canvas的尺寸与视频尺寸一致
    var canvas = document.createElement("canvas");
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    const ctx = canvas.getContext("2d")!;
    // 绘制视频的当前帧到canvas上
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    // 停止视频的加载，避免在后台继续播放
    video.pause();
    // 你可以在这里做进一步的操作，比如保存canvas内容为图片
    var imageDataUrl = canvas.toDataURL("image/png");
    promiseResolve(imageDataUrl);
  });
  video.load();
  // 加载第一帧
  video.currentTime = 1;
  return promise;
}
