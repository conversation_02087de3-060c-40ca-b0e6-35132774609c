<template>
  <div class="flex w-full h-full bg-[#E6F2FB] px-[.44rem] py-[.14rem]">
    <label @click="onSelectAlbum">
      <div>
        <div
          class="w-[1.36rem] h-[1.36rem] rounded-[.16rem] bg-[#fff] flex justify-center items-center"
        >
          <img src="@/assets/xc_ic.png" class="w-[.8rem] h-[.8rem]" alt="" />
        </div>
        <div class="text-[.26rem] text-center py-[.16rem] text-[#a0a0a0]">
          相册
        </div>
      </div>
      <input
        class="hidden"
        type="file"
        accept="video/*,image/*"
        @change="onSelectAlbum"
      />
    </label>
  </div>
</template>

<script setup lang="ts">
import { MsgAction } from "@/api/chat";
import { useIconBut } from "./index";

const emit = defineEmits<{
  (e: "sendMessage", msg: Promise<MsgAction>): void;
}>();

const { onSelectAlbum } = useIconBut({}, emit);
</script>

<style scoped></style>
