<template>
  <div class="relative">
    <div @click.stop="togglePopup">
      <slot></slot>
    </div>
    <Popup
      class="absolute bottom-full left-0 mb-6 shadow-md bg-white rounded-8 flex flex-col item-center"
      v-model="showPicker"
    >
      <div class="flex justify-center mt-16 mb-8 space-x-20">
        <div
          :class="{ 'opacity-50 cursor-pointer': tab !== 0 }"
          @click="tab = 0"
        >
          emoji
        </div>
        <div
          :class="{ 'opacity-50 cursor-pointer': tab !== 1 }"
          @click="tab = 1"
        >
          典典表情包
        </div>
      </div>
      <div class="mx-10 w-304 h-240 overflow-y-auto">
        <div class="flex flex-wrap" v-show="tab === 0">
          <div
            class="hover:bg-gray-200 w-50 m-5 rounded-8"
            v-for="(imgUrl, text) in emojiMapper"
          >
            <img
              :src="imgUrl"
              @click="
                togglePopup();
                emit('pickEmoji', { imgUrl, text });
              "
            />
          </div>
        </div>
        <div class="flex flex-wrap" v-show="tab === 1">
          <div
            class="hover:bg-gray-200 w-90 h-50 m-5"
            v-for="(imgUrl, text) in singleEmojiMapper"
          >
            <img
              class="w-full h-full object-contain"
              :src="imgUrl"
              @click="
                togglePopup();
                emit('pickSingleEmoji', text);
              "
            />
          </div>
        </div>
      </div>
    </Popup>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import Popup from "@/components/Popup.vue";
import { singleEmojiMapper, emojiMapper } from "./mapper";

const emit = defineEmits<{
  (e: "pickEmoji", emoji: { imgUrl: string; text: string }): void;
  (e: "pickSingleEmoji", text: string): void;
}>();

const showPicker = ref(false);
const tab = ref(0);

function togglePopup() {
  showPicker.value = !showPicker.value;
}
</script>
