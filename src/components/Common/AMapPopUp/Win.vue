<!-- 学车地图弹窗 - 桌面版 -->
<template>
  <Model v-if="visibleRef" v-model="visibleRef">
    <div class="bg-[#fff] rounded-5" style="width: 1100px" v-if="visibleRef">
      <div
        class="h-50 border-b-1 border-solid border-[#e5e5e5] flex justify-between items-center px-20"
      >
        <div>学车地图</div>
        <div
          class="w-15 h-15 cursor-pointer"
          v-html="$icon_close"
          @click="visibleRef = false"
        ></div>
      </div>
      <div class="px-20 flex pb-20">
        <div class="flex-1 pr-10">
          <div class="h-70 flex items-center relative">
            <label
              class="w-1/2 border-1 border-solid border-[#e5e5e5] rounded-5 flex items-center px-10 h-35"
            >
              <input
                id="search-input-desktop"
                type="text"
                placeholder="输入学员所在地以搜索"
                class="outline-none flex-1 pr-11"
              />
              <div
                class="w-15 h-15 bg-cover block bg-[url(./assets/top_search.png)]"
              ></div>
            </label>
            <div
              id="search-result-desktop"
              class="absolute bg-[#fff] w-full z-[9999] left-0 top-60 border-1 border-solid border-[#ccc] rounded-5 max-h-[300px] overflow-y-auto"
            ></div>
          </div>

          <div
            id="amap-container"
            class="h-600 border-1 border-solid border-[#ccc] desktop-map"
          ></div>
        </div>
        <div
          class="w-1/4 bg-[#F2FAFF] text-14 px-5 flex flex-col h-670 relative"
        >
          <template v-if="trainingGroundInfoRef">
            <div class="pb-10">
              <div class="text-[#000] font-bold py-10">
                {{ trainingGroundInfoRef?.trainingFieldName }}
              </div>
              <table class="w-full border-collapse">
                <tbody>
                  <tr>
                    <td class="text-[#7F7F7F] w-100 align-top py-2 pr-2">
                      所属驾校：
                    </td>
                    <td class="text-[#333] align-top py-2">
                      {{ trainingGroundInfoRef?.jiaxiaoName }}
                    </td>
                  </tr>
                  <tr>
                    <td class="text-[#7F7F7F] w-100 align-top py-2 pr-2">
                      距离：
                    </td>
                    <td class="text-[#333] align-top py-2">
                      {{ trainingGroundInfoRef?.distanceText }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div
              class="text-13 flex-1 h-0 overflow-auto pb-10"
              v-if="
                trainingGroundInfoRef.classList &&
                trainingGroundInfoRef.classList.length > 0
              "
            >
              <div
                class="bg-[#fff] border-1 border-solid border-[#e5e5e5] rounded-5 p-10 text-[#333] mb-10"
                v-for="item in trainingGroundInfoRef.classList"
                :key="item.classCode"
              >
                <div
                  class="text-[#04a5ff] cursor-pointer"
                  @click="onGoDetail(item)"
                >
                  {{ item.className }}
                </div>
                <div class="flex mt-10">
                  <div class="w-70">商品编码</div>
                  <div>{{ item.goodsCode }}</div>
                </div>
                <div class="flex mt-10">
                  <div class="w-70">价格</div>
                  <div>{{ item.price }}</div>
                </div>
                <div class="h-31 mt-10 flex">
                  <div
                    class="bg-[#04a5ff] text-[#fff] rounded-5 text-center text-14/31 cursor-pointer flex-1"
                    @click="sendJXBMMessage(item)"
                  >
                    学车商品卡片
                  </div>
                  <div class="w-10"></div>
                  <div
                    class="flex-1 border-1 border-solid border-[#ccc] text-center text-14/31 cursor-pointer rounded-5"
                    @click="onReservationClick(item)"
                  >
                    预约表单
                  </div>
                </div>
              </div>
            </div>
          </template>
          <template v-else>
            <div
              class="flex flex-col items-center justify-center h-full text-18"
            >
              请选择训练场
            </div>
          </template>
          <Loading :isLoading="loadingRef"></Loading>
        </div>
      </div>
    </div>
  </Model>
</template>

<script setup lang="ts">
import { watch } from "vue";
import Model from "@/components/Model.vue";
import $icon_close from "@/assets/close.svg?raw";
import { useAMapPopUp } from "./index";
import Loading from "@/components/Loading.vue";
import { ClassList } from "@/api/jxbm";

const {
  visibleRef,
  trainingGroundInfoRef,
  loadingRef,
  open,
  reset,
  sendJXBMMessage,
  onReservationClick,
} = useAMapPopUp();

const onGoDetail = (item: ClassList) => {
  // console.log(item);
  window.open(item.detailPageUrl, "_blank");
};

defineExpose({
  open: (
    sessionKey: string,
    latitude?: number,
    longitude?: number,
    userId?: string,
  ) => {
    open(sessionKey, latitude, longitude, userId);
  },
});

watch(
  () => visibleRef.value,
  () => {
    if (!visibleRef.value) {
      reset();
    }
  },
);
</script>

<style lang="less">
/* 桌面端地图样式 */
.desktop-map {
  .amap-icon-container {
    box-sizing: border-box;
    min-width: 100px;
    // max-width: 200px;
    padding: 5px 10px;
    background-color: #5aa7f5;
    border-radius: 5px;
    color: #fff;
    text-align: center;
    line-height: 20px;
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    transform: translateZ(0);
    /* 不再使用transform来定位，改用bottom属性 */
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    pointer-events: auto; /* 确保点击事件正常工作 */
  }
  .amap-icon-arrow {
    border-top: 10px solid #5aa7f5;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid transparent;
    position: absolute;
    bottom: -20px;
    left: 50%;
    margin-left: -10px;
    z-index: 1; /* 确保箭头在正确的层级 */
  }

  .amap-icon-container.active {
    background-color: #f59a23;
    .amap-icon-arrow {
      border-top: 10px solid #f59a23;
    }
  }

  // .amap-marker {
  //   width: min-content;

  //   .amap-marker-content {
  //     width: min-content;
  //   }
  // }
  .amap-icon img,
  .amap-marker-content img {
    width: 25px;
    height: 34px;
  }

  .marker {
    position: absolute;
    top: -20px;
    right: -118px;
    color: #fff;
    padding: 4px 10px;
    box-shadow: 1px 1px 1px rgba(10, 10, 10, 0.2);
    white-space: nowrap;
    font-size: 12px;
    font-family: "";
    background-color: #25a5f7;
    border-radius: 3px;
  }

  .input-card {
    width: 18rem;
    z-index: 1;
  }

  .input-card .btn {
    margin-right: 0.8rem;
  }

  .input-card .btn:last-child {
    margin-right: 0;
  }
}
</style>
