import { inject, ref } from "vue";

// 高德地图安全配置，必须在引入 AMapLoader 之前设置
(window as any)._AMapSecurityConfig = {
  serviceHost: "https://amap-proxy.mucang.cn/_AMapService",
};

import AMapLoader from "@amap/amap-jsapi-loader";
import { AMAP_KEY } from "@/utils/constant";
import {
  MonaClassGoods,
  queryClassItemList,
  MonaClassItemList,
  sendRecommendClassMsg,
  ClassList,
  sendVisitJiaXiaoMsg,
  queryJiaxiaoGroupGoodsList,
} from "@/api/jxbm";
import { useRouter } from "vue-router";
import { makeToast } from "@/utils/dom";

export interface UserPosition {
  longitude?: number;
  latitude?: number;
}

export function useAMapPopUp() {
  const visibleRef = ref(false);
  const trainingGroundListRef = ref<MonaClassGoods[]>(); // 训练场列表
  const trainingGroundInfoRef = ref<MonaClassItemList>(); // 训练场信息
  const loadingRef = ref(false);
  // 用户位置
  const userPositionRef = ref<UserPosition>({
    longitude: undefined,
    latitude: undefined,
  });
  let isUserPosition = false; // 是否有用户位置
  let amap: any = null; // 高德地图实例;
  let userMarker: any = null; // 用户标记点;
  let sessionKey = ""; // 会话
  let mucangId = ""; // 用户的木仓id
  const customizeMarkerArr: any[] = []; // 自定义标记点
  const terminal = inject("terminal");
  const router = useRouter();
  /**
   * 打开弹窗
   * @param Key 会话
   * @param latitude 纬度
   * @param longitude 经度
   */
  const open = async (
    Key: string,
    latitude?: number,
    longitude?: number,
    userId?: string,
  ) => {
    sessionKey = Key;
    mucangId = userId || "";
    visibleRef.value = true;
    if (latitude !== null && latitude !== undefined) {
      isUserPosition = true;
    }
    await getQueryDashboard(Key, latitude, longitude);
    await aMapInit();
  };

  /**
   * 初始化高德地图
   */
  const aMapInit = () => {
    AMapLoader.load({
      key: AMAP_KEY,
      version: "2.0",
      plugins: ["AMap.ToolBar", "AMap.AutoComplete", "AMap.Geocoder"],
    })
      .then((AMap) => {
        amap = new AMap.Map("amap-container", {
          // 设置地图容器id
          viewMode: "2D",
          zoom: 15, // 初始化地图级别
          center: [
            userPositionRef.value.longitude || 116.397428,
            userPositionRef.value.latitude || 39.90923,
          ], // 初始化地图中心点位置
          resizeEnable: true,
          useRaster: true,
        });
        amap.addControl(
          new AMap.ToolBar({
            liteStyle: true,

            position: "RB", // 位置：左下角
            visible: true,
          }),
        );
        // 创建高德地图的输入联想实例 - 移动端和桌面端分开
        const autoCompleteMobile = new AMap.AutoComplete({
          input: "search-input-mobile",
          output: "search-result-mobile",
          outPutDirAuto: true, // 自动调整下拉框方向
          closeResultOnScroll: false, // 滚动时不关闭结果列表
        });

        const autoCompleteDesktop = new AMap.AutoComplete({
          input: "search-input-desktop",
          output: "search-result-desktop",
          outPutDirAuto: true, // 自动调整下拉框方向
          closeResultOnScroll: false, // 滚动时不关闭结果列表
        });
        if (isUserPosition) {
          userMarker = createUserMarker([
            userPositionRef.value.longitude,
            userPositionRef.value.latitude,
          ]);
        }
        // 地址列表选择 - 移动端和桌面端分别绑定事件
        autoCompleteMobile.on("select", handleSelected);
        autoCompleteDesktop.on("select", handleSelected);

        /**
         * 选中的处理方式
         * @param e
         */
        function handleSelected(e: any) {
          const geocoder = new AMap.Geocoder({
            city: e.poi.adcode, // 精确到区县级（如：河北省张家口市怀来县）
            radius: 1000, // 搜索半径（适配乡村级地名）
          });
          geocoder.getLocation(e.poi.name, (status: any, result: any) => {
            if (status === "complete" && result.geocodes.length) {
              const { lng, lat } = result.geocodes[0].location;
              amap.setCenter([lng, lat], true, 300);
              getQueryDashboard(sessionKey, lat, lng).then(() => {
                trainingGroundInfoRef.value = undefined;
                userMarker = createUserMarker(result.geocodes[0].location); // 添加用户标记
                removeCustomizeMarker(); // 移除自定义标记
                createInteractiveMarker(); // 创建交互标记
              });
            }
          });
        }

        /**
         * 创建用户位置标签
         */
        function createUserMarker(location: any) {
          if (userMarker) {
            amap.remove(userMarker);
          }
          const marker = new AMap.Marker({
            icon: "//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-default.png",
            position: location,
            offset: new AMap.Pixel(-13, -30),
          });
          removeActive(); // 移除激活状态
          amap.add(marker);
          return marker;
        }

        /**
         * 添加自定义标签标记点
         */
        const createInteractiveMarker = () => {
          for (let i = 0; i < trainingGroundListRef.value!.length; i++) {
            const marker = new AMap.Marker({
              position: [
                trainingGroundListRef.value![i].longitude,
                trainingGroundListRef.value![i].latitude,
              ],
              content: `
              <div class="amap-icon-container" key='${i}' trainingFieldId='${trainingGroundListRef.value![i].trainingFieldId}' longitude='${trainingGroundListRef.value![i].longitude}' latitude='${trainingGroundListRef.value![i].latitude}'>
                <div style="width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; text-align: center;">${trainingGroundListRef.value![i].trainingFieldName}</div>
                <div class="amap-icon-arrow"></div>
              </div>
            `,
              anchor: "bottom-center", // 设置锚点为底部中心
              offset: new AMap.Pixel(0, 0), // 设置偏移，确保标记位置准确
              zIndex: 100, // 设置层级，确保标记在其他元素上方
            });
            customizeMarkerArr.push(marker);
            amap.add(marker);
            /**
             * 点击文本标签
             */
            marker.on("click", async () => {
              await onTextMarkerClick(String(i));
            });
          }
        };
        createInteractiveMarker();
      })
      .catch((e) => {
        console.log(e);
      });
  };

  /**
   * 点击文本标记
   * @param {string} index 索引
   */
  async function onTextMarkerClick(index: string) {
    const markerDomArr = document
      .querySelector("#amap-container")
      ?.querySelectorAll(".amap-icon-container") as any as HTMLElement[];
    for (let i = 0; i < markerDomArr?.length; i++) {
      if (markerDomArr[i].getAttribute("key") === index) {
        markerDomArr[i].classList.add("active");
        const trainingFieldId = Number(
          markerDomArr[i].getAttribute("trainingFieldId"),
        );
        const longitude = Number(markerDomArr[i].getAttribute("longitude"));
        const latitude = Number(markerDomArr[i].getAttribute("latitude"));
        const trainingFieldIdStr = trainingGroundListRef.value
          ?.filter((item) => {
            return item.longitude === longitude && item.latitude === latitude;
          })
          .map((item) => item.trainingFieldId)
          .sort((a) => (a === trainingFieldId ? -1 : 0))
          .join(",");

        getClassItemList(
          trainingFieldIdStr || "",
          userPositionRef.value.latitude,
          userPositionRef.value.longitude,
        );
      } else {
        markerDomArr[i].classList.remove("active"); // 切换激活状态
      }
    }
  }

  // 移除激活状态
  function removeActive() {
    const markerDomArr = document
      .querySelector("#amap-container")
      ?.querySelectorAll(".amap-icon-container") as any as HTMLElement[];
    for (let i = 0; i < markerDomArr?.length; i++) {
      markerDomArr[i].classList.remove("active"); // 切换激活状态
    }
  }

  /**
   * 获取班级列表
   * @param trainingFieldId 训练场id
   * @param latitude
   * @param longitude
   */
  const getClassItemList = async (
    trainingFieldIds: string,
    latitude?: number,
    longitude?: number,
  ) => {
    loadingRef.value = true;
    trainingGroundInfoRef.value = await queryClassItemList({
      trainingFieldIds,
      latitude,
      longitude,
    });
    loadingRef.value = false;
  };

  /**
   * 重置
   */
  const reset = () => {
    trainingGroundListRef.value = [];
    trainingGroundInfoRef.value = undefined;
    userPositionRef.value.latitude = undefined;
    userPositionRef.value.longitude = undefined;
    isUserPosition = false;
    userMarker = null;
    amap?.destroy();
  };

  /**
   * 发送驾校报名消息
   * @param value 班级信息
   */
  const sendJXBMMessage = async (value: ClassList) => {
    await sendRecommendClassMsg({
      sessionKey: sessionKey,
      classCode: value.classCode,
      goodsCode: value.goodsCode,
    });
    visibleRef.value = false;
    if (terminal) {
      router.back();
    }
  };

  /**
   * 移除所有自定义标记
   */
  const removeCustomizeMarker = () => {
    if (customizeMarkerArr.length <= 0) {
      return;
    }
    customizeMarkerArr.forEach((marker) => {
      amap.remove(marker);
    });
  };

  /**
   * 获取训练场列表
   * @param Key
   */
  const getQueryDashboard = async (
    Key: string,
    latitude?: number,
    longitude?: number,
  ) => {
    userPositionRef.value.latitude = latitude;
    userPositionRef.value.longitude = longitude;
    const res = await queryJiaxiaoGroupGoodsList({
      sessionKey: Key,
      latitude,
      longitude,
    });
    trainingGroundListRef.value = res.itemList
      .map((item) => item.goodsList)
      .flat();
  };
  /**
   * 点击预约表单
   */
  const onReservationClick = async (item: ClassList) => {
    loadingRef.value = true;
    const { value } = await sendVisitJiaXiaoMsg({
      sessionKey: sessionKey,
      userId: mucangId,
      classCode: item.classCode,
      goodsCode: item.goodsCode,
    }).finally(() => {
      loadingRef.value = false;
    });
    if (value) {
      makeToast("发送成功");
      visibleRef.value = false;
      if (terminal) {
        router.back();
      }
    } else {
      makeToast("发送失败");
    }
  };

  return {
    visibleRef,
    trainingGroundListRef,
    trainingGroundInfoRef,
    loadingRef,
    userPositionRef,
    open,
    reset,
    sendJXBMMessage,
    onReservationClick,
  };
}
