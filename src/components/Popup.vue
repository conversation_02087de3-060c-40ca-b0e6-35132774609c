<template>
  <div v-if="visible" ref="dockerNode">
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from "vue";

const visible = defineModel<boolean>();
const dockerNode = ref<HTMLElement>();

function clickOutSide(e: Event) {
  const target = e.target as HTMLElement;
  if (visible.value && dockerNode.value && dockerNode.value.contains(target)) {
    return;
  }
  visible.value = false;
}

onMounted(() => {
  document.addEventListener("click", clickOutSide);
});
onUnmounted(() => {
  document.removeEventListener("click", clickOutSide);
});
</script>
