import { createApp } from "vue";
import "@/style.css";
import App from "./App.vue";
import store from "@/store/index";

import { init } from "@/initialize/init";
import { EventEmitter } from "@/utils/eventEmitter";
import resize from "@/directives/resize";
const terminal: number = init();

const app = createApp(App);
app.provide("terminal", terminal);
app.provide("emitter", new EventEmitter());
app.directive("resize", resize);
app.use(store);
app.mount("#app");
