export default [
  {
    name: "/",
    path: "/",
    component: () => import("@/pages/App/Home.vue"),
    meta: {
      title: "木仓聊天室",
    },
  },
  {
    name: "AMapPopUp",
    path: "/AMapPopUp",
    component: () => import("@/pages/App/AMapPopUp.vue"),
    meta: {
      title: "学车地图",
    },
  },
  {
    name: "search",
    path: "/search",
    component: () => import("@/pages/App/Search.vue"),
    meta: {
      title: "搜索",
    },
  },
  {
    name: "chat",
    path: "/chat",
    component: () => import("@/pages/App/Chat.vue"),
    meta: {},
  },
  {
    name: "AI",
    path: "/AI",
    component: () => import("@/pages/App/AI.vue"),
    meta: {
      title: "AI回复",
    },
  },
  {
    name: "AIScript",
    path: "/AIScript",
    component: () => import("@/pages/App/AIScript.vue"),
    meta: {
      title: "AI自动回复",
    },
  },
  {
    name: "CustomerDetail",
    path: "/CustomerDetail/:type",
    component: () => import("@/pages/App/CustomerDetail.vue"),
  },
  {
    name: "Instructions",
    path: "/Instructions",
    component: () => import("@/pages/App/Instructions.vue"),
    meta: {
      title: "快捷指令",
    },
  },
  {
    name: "JXBM",
    path: "/JXBM",
    component: () => import("@/pages/App/JXBM.vue"),
    meta: {
      title: "用户画像",
    },
  },
  {
    name: "MemberList",
    path: "/MemberList",
    component: () => import("@/pages/App/MemberList.vue"),
    meta: {
      title: "群设置",
    },
  },
  {
    name: "QA",
    path: "/QA",
    component: () => import("@/pages/App/QA.vue"),
    meta: {
      title: "常见问题回复",
    },
  },
  {
    name: "WordLibrary",
    path: "/WordLibrary",
    component: () => import("@/pages/App/WordLibrary.vue"),
    meta: {
      title: "话术库",
    },
  },
  {
    name: "AccountSelect",
    path: "/AccountSelect",
    component: () => import("@/pages/App/AccountSelect.vue"),
    meta: {
      title: "身份选择",
    },
  },
  {
    name: "WebPage",
    path: "/WebPage",
    component: () => import("@/pages/App/WebPage.vue"),
  },
  {
    name: "UserOrder",
    path: "/UserOrders",
    component: () => import("@/pages/App/UserOrders.vue"),
    meta: {
      title: "用户订单",
    },
  },
  {
    name: "LabelList",
    path: "/LabelList",
    component: () => import("@/pages/App/LabelList.vue"),
    meta: {
      title: "标签列表",
    },
  },
  {
    name: "IntentionForm",
    path: "/IntentionForm",
    component: () => import("@/pages/App/IntentionForm.vue"),
    meta: {
      title: "意向表单",
    },
  },
];
