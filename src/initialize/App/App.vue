<template>
  <div class="pageStack">
    <transition-group
      :name="transitionName"
      mode="out-in"
      :css="!!transitionName"
    >
      <div
        class="page"
        v-show="i === routeStack.length - 1"
        v-for="(route, i) in routeStack"
        :key="route.fullPath"
      >
        <router-view :route="route" v-slot="{ Component }">
          <component :is="Component" />
        </router-view>
      </div>
    </transition-group>
  </div>
</template>

<script lang="ts">
import { defineComponent, markRaw } from "vue";
import { RouteLocationNormalizedLoaded } from "vue-router";
import { last } from "lodash";
import { RouterView } from "@/features/router/routerView";
import { isIOS } from "@/utils/helpers";

export default defineComponent({
  components: {
    RouterView,
  },
  data() {
    return {
      routeStack: [] as RouteLocationNormalizedLoaded[],
      navigationType: "push",
      transitionName: "",
    };
  },
  watch: {
    $route(to, _from) {
      this.updateStack(to);
    },
  },
  mounted() {
    const interceptRouterMethod = (router: any, method: any) => {
      const fn = router[method];
      router[method] = (...args: any[]) => {
        this.navigationType = method;
        return fn(...args);
      };
    };
    interceptRouterMethod(this.$router, "go");
    interceptRouterMethod(this.$router, "back");
    interceptRouterMethod(this.$router, "push");
    interceptRouterMethod(this.$router, "replace");
    this.$router.afterEach((to) => {
      const title = (to.query.title || to.meta.title) as string;
      if (title) {
        document.title = title;
      }
    });
  },
  methods: {
    updateStack(to: RouteLocationNormalizedLoaded) {
      console.log("router", this.navigationType);
      to = markRaw(to);

      if (this.navigationType === "push") {
        if (this.routeStack.length) {
          this.transitionName = "push";
        }

        const before = last(this.routeStack)!;
        this.routeStack.push(to);

        if (before) {
          this.$nextTick(() => {
            const pageComponent = before.matched[0].instances[before.fullPath];
            if (pageComponent && !pageComponent.$.isUnmounted) {
              (pageComponent as any).onHide?.();
            }
          });
        }
      } else if (this.navigationType === "replace") {
        this.transitionName = "push";

        this.routeStack[this.routeStack.length - 1] = to;
      } else {
        if (!isIOS) {
          this.transitionName = "back";
        } else {
          this.transitionName = "";
        }

        const stackIndex = this.routeStack.findIndex(
          (route) => route.matched[0] === to.matched[0],
        );
        this.routeStack = this.routeStack.slice(0, stackIndex + 1);
        const after = last(this.routeStack)!;
        this.$nextTick(() => {
          const pageComponent = after.matched[0].instances[after.fullPath];
          if (pageComponent && !pageComponent.$.isUnmounted) {
            (pageComponent as any).onShow?.();
          }
        });
      }

      // 用完重置一下，因为可能有系统返回
      this.navigationType = "back";
    },
    headleEnter() {},
  },
});
</script>
