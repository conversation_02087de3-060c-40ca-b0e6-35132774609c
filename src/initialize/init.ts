let terminal: number = 0; // pc
function adjustFontSize(doc: Document, designWidth: number) {
  var html = doc.documentElement;
  function refreshFontSize() {
    var clientWidth = html.clientWidth;
    var baseFontSize = 100; // 基础字体大小
    var fontSize =
      clientWidth >= designWidth
        ? baseFontSize
        : baseFontSize * (clientWidth / designWidth);
    html.style.fontSize = `${fontSize}px`;
  }
  doc.addEventListener("DOMContentLoaded", refreshFontSize);
  window.addEventListener("resize", refreshFontSize);
}

export const init = (): number => {
  const w = document.documentElement.clientWidth;
  if (w < 1024) {
    terminal = 1;
    adjustFontSize(document, 750);
  }
  return terminal;
};
