<template>
  <div class="pageStack">
    <transition-group
      :name="transitionName"
      mode="out-in"
      :css="!!transitionName"
    >
      <div
        class="page"
        v-show="i === routeStack.length - 1"
        v-for="(route, i) in routeStack"
        :key="route.fullPath"
      >
        <router-view :route="route" v-slot="{ Component }">
          <component :is="Component" />
        </router-view>
      </div>
    </transition-group>

    <!-- 悬浮返回按钮 -->
    <div v-if="canGoBack" class="floating-back-btn" @click="handleBack">
      <img src="@/assets/backArrow.svg" alt="返回" class="back-icon" />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, markRaw } from "vue";
import { RouteLocationNormalizedLoaded } from "vue-router";
import { last } from "lodash";
import { RouterView } from "@/features/router/routerView";
import { isIOS } from "@/utils/helpers";

export default defineComponent({
  components: {
    RouterView,
  },
  data() {
    return {
      routeStack: [] as RouteLocationNormalizedLoaded[],
      navigationType: "push",
      transitionName: "",
    };
  },
  computed: {
    canGoBack(): boolean {
      return this.routeStack.length > 1;
    },
  },
  watch: {
    $route(to, _from) {
      this.updateStack(to);
    },
  },
  mounted() {
    const interceptRouterMethod = (router: any, method: any) => {
      const fn = router[method];
      router[method] = (...args: any[]) => {
        this.navigationType = method;
        return fn(...args);
      };
    };
    interceptRouterMethod(this.$router, "go");
    interceptRouterMethod(this.$router, "back");
    interceptRouterMethod(this.$router, "push");
    interceptRouterMethod(this.$router, "replace");
    this.$router.afterEach((to) => {
      const title = (to.query.title || to.meta.title) as string;
      if (title) {
        document.title = title;
      }
    });
  },
  methods: {
    handleBack() {
      if (this.canGoBack) {
        this.$router.back();
      }
    },
    updateStack(to: RouteLocationNormalizedLoaded) {
      console.log("router", this.navigationType);
      to = markRaw(to);

      if (this.navigationType === "push") {
        if (this.routeStack.length) {
          this.transitionName = "push";
        }

        const before = last(this.routeStack)!;
        this.routeStack.push(to);

        if (before) {
          this.$nextTick(() => {
            const pageComponent = before.matched[0].instances[before.fullPath];
            if (pageComponent && !pageComponent.$.isUnmounted) {
              (pageComponent as any).onHide?.();
            }
          });
        }
      } else if (this.navigationType === "replace") {
        this.transitionName = "push";

        this.routeStack[this.routeStack.length - 1] = to;
      } else {
        if (!isIOS) {
          this.transitionName = "back";
        } else {
          this.transitionName = "";
        }

        const stackIndex = this.routeStack.findIndex(
          (route) => route.matched[0] === to.matched[0],
        );
        this.routeStack = this.routeStack.slice(0, stackIndex + 1);
        const after = last(this.routeStack)!;
        this.$nextTick(() => {
          const pageComponent = after.matched[0].instances[after.fullPath];
          if (pageComponent && !pageComponent.$.isUnmounted) {
            (pageComponent as any).onShow?.();
          }
        });
      }

      // 用完重置一下，因为可能有系统返回
      this.navigationType = "back";
    },
    headleEnter() {},
  },
});
</script>

<style scoped>
.floating-back-btn {
  position: fixed;
  bottom: 20px;
  left: 20px;
  z-index: 1000;
  width: 44px;
  height: 44px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.floating-back-btn:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transform: scale(1.05);
}

.floating-back-btn:active {
  transform: scale(0.95);
}

.back-icon {
  width: 20px;
  height: 20px;
  filter: brightness(0.7);
}

.floating-back-btn:hover .back-icon {
  filter: brightness(0.9);
}
</style>
