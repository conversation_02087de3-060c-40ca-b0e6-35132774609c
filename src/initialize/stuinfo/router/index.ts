import { createWebHashHistory, createRouter } from "vue-router";
import routes from "./routes";
import h5Hash from "@/utils/h5Hash";
// 因h5分享链接可能从pc分享而来，需要将hash进行存在
if (location.hash) {
  console.log("原始hash", location.hash);
  h5Hash.set(location.hash);
}
// 链接可能从pc分享而来，重置hash，不允许直接跳内部页面
location.hash = "";

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes,
});

export default router;
