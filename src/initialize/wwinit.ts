import { getAgentConfigSignatureStore, GetCorpWxUserIdStore } from "@/api/wx";
import * as ww from "@wecom/jssdk";
import { URLParams } from "@/utils/tools";
import { SignatureData } from "@wecom/jssdk";
import { once } from "lodash";

const weixinCorpId = "ww256ae133a9efac22";
const agentId = 1000017;
// sessionStorage.setItem('externalUserId', 'wmSe6aFwAAiMfeRWAFG9uyIb21-Y6-Og');
// localStorage.setItem('corpWxUserId', 'HouFang');

let externalUserId = sessionStorage.getItem("externalUserId") || "";
let corpWxUserId = localStorage.getItem("corpWxUserId") || "";

const getAgentConfigSignature = () => {
  return new Promise<SignatureData>(async (resolve) => {
    const res = await getAgentConfigSignatureStore({
      weixinCorpId: weixinCorpId,
      agentId: agentId,
      url: location.origin + location.pathname + location.search,
    });
    console.log("getAgentConfigSignatureStore", res);
    let { timestamp, noncestr, signature } = res;
    resolve({ timestamp, nonceStr: noncestr, signature });
  });
};

const InitAgentConfig = () => {
  return new Promise((resolve, reject) => {
    console.log("initagentcofnig", ww);
    ww.register({
      corpId: weixinCorpId,
      agentId: agentId,
      jsApiList: [
        "getCurExternalContact",
        "getCurExternalChat",
        "openEnterpriseChat",
        "sendChatMessage",
      ],
      getAgentConfigSignature,
      onAgentConfigSuccess() {
        console.log("onAgentConfigSuccess");
        resolve("success");
      },
      onConfigFail(error) {
        console.log("onConfigFail", error);
        reject(error);
      },
      onAgentConfigFail(error) {
        console.log("onAgentConfigFail", error);
        reject(error);
      },
    });
  });
};

// 获取当前外部联系人userId
const GetCurExternalContact = () => {
  return new Promise(async (resolve, reject) => {
    if (externalUserId) {
      resolve(externalUserId);
    } else {
      const result = await ww.getCurExternalContact();
      console.log("getCurExternalContact---", result);
      if (result.errMsg === "getCurExternalContact:ok") {
        externalUserId = result.userId;
        sessionStorage.setItem("externalUserId", result.userId);
        resolve(result.userId);
      } else {
        // 错误处理
        reject("获取当前外部联系人失败");
      }
    }
  });
};

const GetCorpWxUserId = () => {
  return new Promise(async (resolve) => {
    console.log("获取 corpWxUserId");
    if (corpWxUserId) {
      console.log("corpWxUserId exist--", corpWxUserId);
      resolve(corpWxUserId);
      return;
    }
    const redirectUrl = encodeURIComponent(
      location.origin + location.pathname + location.search,
    );
    const redirectUri = `https://share-m.kakamobi.com/activity.kakamobi.com/jiakaobaodian-chores/redirectWxWorkCode.html?redirectUrl=${encodeURIComponent(redirectUrl)}`;
    const loginUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${weixinCorpId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_privateinfo&state=STATE&agentid=${agentId}#wechat_redirect`;
    const weixinCode = URLParams.get("code");

    if (weixinCode) {
      const result = await GetCorpWxUserIdStore({
        code: weixinCode,
        agentId: agentId,
      });
      console.log("GetCorpWxUserIdStore data--", result);
      if (result.userId) {
        localStorage.setItem("corpWxUserId", result.userId);
        corpWxUserId = result.userId;

        resolve(corpWxUserId);
      }
    } else {
      window.location.href = loginUrl;
    }
  });
};

export const initWxWork = once(async () => {
  const initRes = await InitAgentConfig();
  if (initRes === "success") {
    await GetCorpWxUserId();
    await GetCurExternalContact();
  }
});
