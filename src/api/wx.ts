import request from "@/utils/request";

export interface SignatureDataType {
  agentId: number;
  jsapiTicket: string;
  url: string;
  /**
   * 生成签名的时间戳
   */
  timestamp: string | number;
  /**
   * 生成签名的随机串
   */
  noncestr: string;
  /**
   * 签名
   */
  signature: string;
}
export function getAgentConfigSignatureStore(data: {
  weixinCorpId: string;
  agentId: number;
  url: string;
}) {
  return request<SignatureDataType>({
    url: "/api/open/server/jssign.htm",
    hostName: "weixin",
    data: data,
  });
}

export function GetCorpWxUserIdStore(data: {
  code: string;
  agentId: string | number;
}) {
  return request<{ userId: string }>({
    url: "/api/web/wechat/auth.htm",
    data: data,
    hostName: "mona",
  });
}
