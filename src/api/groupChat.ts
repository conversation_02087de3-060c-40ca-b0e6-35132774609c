import request from "@/utils/request";

export interface GroupMemberList {
  avatar: string;
  customerId: string;
  muted: boolean;
  nickname: string;
  role: 1 | 2 | 3 | 4 | 5;
}
interface UpdateGroupName {
  sessionKey: string;
  groupName: string;
}

interface ForbidSendMsg {
  sessionKey: string; // 群会话id
  customerIdList: string; // 禁言用户id
  muteTime: 0 | -1; // 禁言时间，0为解禁，-1为永久禁言
}

interface AddMember {
  sessionId: string; // 群会话id
  memberList: string; // 禁言用户id
}

interface UpdateBulletin {
  sessionKey: string; // 群会话key
  groupBulletin: string; // 群公告
}

interface CallRecord {
  id: number;
  callUserId: string;
  launchUserId: string;
  bizId: number;
  /**通话状态 */
  status: 1 | 2 | 3;
  sceneType: number;
  createTime: string;
  direction: number;
  channel: number;
  launchType: number;
  createUserId: number;
  createUserName: string;
  answer: boolean;
}

/**
 * 获取群成员
 */
export const getGroupMember = (sessionKey: string) => {
  return request<{
    itemList: GroupMemberList[];
  }>({
    hostName: "parrot",
    method: "GET",
    url: "api/admin/pt-student-session/get-session-member-list.htm",
    data: {
      sessionKey,
    },
  });
};

/**
 * 修改群名称
 * @param data
 * @returns
 */
export const updateGroupName = (data: UpdateGroupName) => {
  return request<{
    value: boolean;
  }>({
    hostName: "chat",
    method: "POST",
    url: "/api/admin/chat-group/update-group-name-by-session-key.htm",
    data,
  });
};
/**
 * 禁言/解禁
 * @param data
 * @returns
 */
export const forbidSendMsg = (data: ForbidSendMsg) => {
  return request<{
    value: boolean;
  }>({
    hostName: "parrot",
    method: "POST",
    url: "/api/admin/pt-student-session/forbid-send-msg.htm",
    data,
  });
};
/**
 * 添加群成员
 * @param data
 * @returns
 */
export const addMember = (data: AddMember) => {
  return request<{
    value: boolean;
  }>({
    hostName: "chat",
    method: "POST",
    url: "/api/admin/chat-group/import-member.htm",
    data,
  });
};
/**
 * 修改群公告
 * @param data
 * @returns
 */
export const updateBulletin = (data: UpdateBulletin) => {
  return request<{
    value: boolean;
  }>({
    hostName: "chat",
    method: "POST",
    url: "/api/admin/chat-group/update-chat-group-info-by-session-key.htm",
    data,
  });
};

/**
 * 更新群头像
 * @param data
 */
export const updateGroupAvatar = (data: {
  sessionKey: string;
  faceUrl: string;
}) => {
  return request<{
    value: boolean;
  }>({
    hostName: "chat",
    method: "POST",
    url: "/api/admin/chat-group/update-face-url-by-session-key.htm",
    data,
  });
};

/**
 * 提醒讲师上课
 */
export const notifyLecturerToTeach = (sessionKey: string) => {
  return request<{
    value: number;
  }>({
    hostName: "parrot",
    method: "GET",
    url: "api/admin/pt-student-session/notify-lecturer-to-teach.htm",
    data: {
      sessionKey,
    },
  });
};

/**
 * 提醒学员上课
 */
export const notifyStudentToTeach = (sessionKey: string) => {
  return request<{
    value: number;
  }>({
    hostName: "parrot",
    method: "GET",
    url: "api/admin/pt-student-session/notify-student-to-teach.htm",
    data: {
      sessionKey,
    },
  });
};

/**
 * 获取呼叫记录详情
 */
export const getCallRecordDetail = (recordId: number) => {
  return request<CallRecord>({
    hostName: "parrot",
    method: "GET",
    url: "api/admin/call-record/view.htm",
    data: {
      recordId,
    },
  });
};
