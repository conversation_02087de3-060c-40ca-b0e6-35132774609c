import request from "@/utils/request";
import { _MsgAction } from "./chat";
import { URLParams } from "@/utils/tools";

export interface CategoryList {
  itemList: {
    categoryName: string;
    categoryType: string;
  }[];
}

export interface InstructionList {
  itemList: {
    categoryName: string;
    commands: {
      commandDesc: string;
      type: _MsgAction["msgType"];
    }[];
    instructionKey: string;
    instructionName: string;
    renderTag: "p1" | "p2" | "p3";
  }[];
}

export interface InstructionSearchType {
  categoryName?: string;
  instructionName?: string;
}

export const getInstructionList = (data: InstructionSearchType) => {
  return request<InstructionList>({
    hostName: "parrot",
    url: "/api/admin/sale-chat/instruction-list.htm",
    data: {
      bizCode: URLParams.get("bizCode") || "10001",
      ...data,
    },
  });
};

export const getCategoryList = async () => {
  return await request<CategoryList>({
    hostName: "parrot",
    url: "/api/admin/sale-chat/instraction-category-list.htm",
  });
};

export const getMonaInstructionList = (data: InstructionSearchType) => {
  return request<InstructionList>({
    hostName: "mona",
    url: "/api/admin/bot/get-instructions.htm",
    data: {
      bizCode: URLParams.get("bizCode") || "10001",
      ...data,
    },
  });
};

export const getMonaCategoryList = async () => {
  return await request<CategoryList>({
    hostName: "mona",
    url: "/api/admin/bot/get-categories.htm",
  });
};
