import { Store } from "@simplex/simple-base-sso";
import { makeToast } from "./dom";
import { URLParams, isTest } from "./tools";
import { isWxwork } from "./platform";

export const HostNames = isTest
  ? {
      chat: "https://chat-platform.ttt.mucang.cn/",
      mona: "https://mona.ttt.mucang.cn/",
      parrot: "https://parrot-admin.ttt.mucang.cn/",
      malibu: "https://malibu.ttt.mucang.cn",
      swallow: "https://swallow.kakamobi.cn/",
      weixin: "https://weixin-manager.mucang.cn",
    }
  : {
      chat: "https://chat-platform.mucang.cn/",
      mona: "https://mona.kakamobi.cn/",
      parrot: "https://parrot-admin.kakamobi.cn/",
      malibu: "https://malibu.kakamobi.cn",
      swallow: "https://swallow.ttt.mucang.cn/",
      weixin: "https://weixin-manager.mucang.cn",
    };

interface RequestOptions {
  hostName?: keyof typeof HostNames; // 请求域名
  url: string;
  method?: "GET" | "POST";
  headers?: Record<string, any>;
  data?: Record<string, any>;
}

/** url签名 */
function sign(a: number): string {
  const c = Math.abs(
    parseInt(new Date().getTime() * Math.random() * 10000 + ""),
  ).toString();
  let d: any = 0;
  for (let b = 0; b < c.length; b++) {
    d += parseInt(c[b]);
  }
  const e = ((f: any) => {
    return (g: string | any[], h: number) => {
      return h - 0 + g.length <= 0
        ? g
        : (f[h] || (f[h] = Array(h + 1).join("0"))) + g;
    };
  })([]);

  d += c.length;
  d = e(d, 3 - d.toString().length);
  return a.toString() + c + d;
}

// 将接口地址转换成在线成交服务的接口地址
const changeApiAddress = (url: string) => {
  const prefix1 = "api/admin/im";
  const prefix2 = "api/admin";
  if (url.indexOf(prefix1) > -1) {
    url = url.replace(/api\/admin\/im/gi, "api/web/wechat");
  } else if (url.indexOf(prefix2) > -1) {
    url = url.replace(/\api\/admin/gi, "api/web");
  }
  return url;
};

export default async function request<T>({
  hostName = "chat",
  url,
  method = "GET",
  headers,
  data = {},
  ...extra
}: RequestOptions) {
  if (hostName === "chat") {
    url += `?bizCode=${URLParams.get("bizCode") || "10001"}`;
  }
  url += (url.indexOf("?") === -1 ? "?" : "&") + `_r = ${sign(1)}`;
  if (isWxwork && hostName === "mona") {
    url = changeApiAddress(url);
    const externalUserId = sessionStorage.getItem("externalUserId") || "";
    const corpWxUserId = localStorage.getItem("corpWxUserId") || "";
    data.externalUserId = externalUserId;
    data.saleUserId = corpWxUserId;
  }

  return new Promise<T>((resolve, reject) => {
    Store.extend({
      apiCors: false,
      // 是否启动接口提示未登录时，自动登录 sso。默认不开启
      autoLogin: true,
      url: `${HostNames[hostName]}${url}`,
      headers,
      method: method.toLocaleUpperCase(),
      errorToast: false,
      type: "online",
      ...extra,
    })
      .create()
      .request(data)
      .then((res: any) => {
        resolve(res);
      })
      .fail((_errorCode: any, error: any) => {
        reject(error);
        makeToast(error.statusText || "服务器错误");
      });
  });
}
