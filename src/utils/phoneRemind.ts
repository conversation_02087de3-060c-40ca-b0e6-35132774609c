/**
 * 存储当前是呼叫中的sessionKey和对应的 通话记录id(recordId)
 */

import { createDraggableBox } from "./dom";
import { getCallRecordDetail } from "@/api/groupChat";

interface PhoneInfo {
  sessionKey: string | null;
  isRemind: boolean;
  role: 5 | 4 | null;
  name: string | null;
  groupName: string | null;
  recordId: number | null;
}

const draggableButton = {
  success: (fn?: () => void) => {
    const button = document.createElement("button");
    button.style.padding = "2px 10px";
    button.style.borderRadius = "5px";
    button.style.backgroundColor = "#33bf4f";
    button.style.color = "#fff";
    button.innerText = "已接听，关闭";
    button.addEventListener("click", () => {
      fn && fn();
    });
    return button;
  },
  fail: (fn?: () => void) => {
    const button = document.createElement("button");
    button.style.padding = "2px 10px";
    button.style.borderRadius = "5px";
    button.style.backgroundColor = "#fc4716";
    button.style.color = "#fff";
    button.innerText = "未接听，关闭";
    button.addEventListener("click", () => {
      fn && fn();
    });
    return button;
  },
};
const PHONE_REMIND_KEY = "phone_remind_key";
const phoneRemind = (function () {
  let instance: any = null;
  class PhoneRemind {
    protected phoneInfo: PhoneInfo = {
      sessionKey: null, // 当前呼叫的sessionKey
      isRemind: false, // 是否呼叫中
      role: null, // 当前呼叫的角色
      name: null, // 当前呼叫的姓名
      groupName: null, // 当前呼叫的群组名
      recordId: null, // 当前呼叫的通话记录id
    };
    constructor({
      sessionKey,
      isRemind,
      role,
      name,
      groupName,
      recordId,
    }: PhoneInfo) {
      if (instance) {
        return instance; // 已存在实例则直接返回
      }
      instance = this; // 首次创建，保存实例
      this.phoneInfo.isRemind = isRemind || false;
      this.phoneInfo.sessionKey = sessionKey || null;
      this.phoneInfo.role = role || null;
      this.phoneInfo.name = name || null;
      this.phoneInfo.groupName = groupName || null;
      this.phoneInfo.recordId = recordId || null;
    }
    getSessionKey() {
      return this.phoneInfo.sessionKey;
    }
    getIsRemind() {
      return this.phoneInfo.isRemind;
    }
    getRole() {
      return this.phoneInfo.role;
    }
    getName() {
      return this.phoneInfo.name;
    }
    getGroupName() {
      return this.phoneInfo.groupName;
    }
    getRecordId() {
      return this.phoneInfo.recordId;
    }

    setPhoneInfo(
      sessionKey: string,
      isRemind: boolean,
      role: 5 | 4,
      name: string,
      groupName: string,
      recordId: number,
    ) {
      localStorage.setItem(
        PHONE_REMIND_KEY,
        JSON.stringify({
          sessionKey,
          isRemind,
          role,
          name,
          groupName,
          recordId,
        }),
      );
      this.phoneInfo.sessionKey = sessionKey;
      this.phoneInfo.isRemind = isRemind;
      this.phoneInfo.role = role;
      this.phoneInfo.name = name;
      this.phoneInfo.groupName = groupName;
      this.phoneInfo.recordId = recordId;
    }

    clearPhoneInfo() {
      localStorage.removeItem(PHONE_REMIND_KEY);
      this.phoneInfo.sessionKey = null;
      this.phoneInfo.isRemind = false;
      this.phoneInfo.role = null;
      this.phoneInfo.name = null;
      this.phoneInfo.groupName = null;
      this.phoneInfo.recordId = null;
    }
    /**
     * 轮询获取通话记录
     * @returns 2:已接听 3:未接听
     */
    pollGetCallRecord(): Promise<2 | 3> {
      return new Promise((resolve, reject) => {
        const attemptFetch = () => {
          getCallRecordDetail(this.phoneInfo.recordId!)
            .then((res) => {
              if (res.status === 1) {
                setTimeout(attemptFetch, 5000);
              } else {
                resolve(res.status);
              }
            })
            .catch((error) => {
              reject(error);
            });
        };
        attemptFetch();
      });
    }

    /**
     * 处理电话状态,打开全局的拖动弹窗
     */
    handleCallStatus(fn?: () => void) {
      const closeCallBlack = () => {
        this.clearPhoneInfo();
        fn && fn();
      };
      const { close, updateContent } = createDraggableBox({
        class: "phone-remind-container",
        title: this.phoneInfo.groupName!,
        showClose: true,
        showCloseFn: closeCallBlack,
        content: `${this.phoneInfo.name}拨打中...`,
      });
      this.pollGetCallRecord()
        .then((callStatus) => {
          if (callStatus === 2) {
            const content = draggableButton.success(close);
            updateContent(content);
          } else if (callStatus === 3) {
            const content = draggableButton.fail(close);
            updateContent(content);
          } else {
            throw new Error("未知状态");
          }
        })
        .finally(() => {
          closeCallBlack();
        });
    }
  }
  return PhoneRemind;
})();

const phoneRemindStorage = JSON.parse(
  localStorage.getItem(PHONE_REMIND_KEY) || "{}",
);

export default new phoneRemind(phoneRemindStorage);
