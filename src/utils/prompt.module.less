.prompt-container {
  box-sizing: border-box;
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.prompt-main {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 1px 1px 10px #ccc;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 10px;
  row-gap: 10px;
  .title {
    font-weight: bold;
  }
}
.prompt-input {
  input {
    margin-top: 5px;
    border: 1px solid #ccc;
    width: 100%;
  }
}
.prompt-but {
  align-self: flex-end;
  button {
    border-radius: 5px;
    &:first-child {
      border: 1px solid #ccc;
      color: #333;
    }
    &:last-child {
      background-color: #1777ff;
      color: #fff;
    }
  }
}

.prompt-win {
  width: 350px;
  .title {
    font-size: 16px;
  }
  .input {
    font-size: 14px;
  }
  .but {
    margin-top: 20px;
    button {
      width: 60px;
      height: 30px;
      font-size: 14px;
    }
  }
}

.prompt-app {
  width: 6rem;
  .title {
    font-size: 0.4rem;
  }
  .input {
    font-size: 0.14rem;
  }
  .but {
    margin-top: 0.05rem;
    button {
      width: 1.3rem;
      height: 0.6rem;
      line-height: 0.6rem;
      font-size: 0.4rem;
    }
  }
}
