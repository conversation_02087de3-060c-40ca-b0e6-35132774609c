/** 时间展示 */
export function dateFormat(
  dateValue: string | number | Date,
  formatStr = "yyyy-MM-dd hh:mm",
  showDayType = 12,
) {
  let timestamp = new Date(dateValue);
  let fmt = formatStr;
  let k;
  const week = [
    "星期日",
    "星期一",
    "星期二",
    "星期三",
    "星期四",
    "星期五",
    "星期六",
  ];
  const o = {
    "M+": timestamp.getMonth() + 1,
    "d+": timestamp.getDate(),
    "h+": timestamp.getHours() % showDayType,
    "H+": timestamp.getHours(),
    "m+": timestamp.getMinutes(),
    "s+": timestamp.getSeconds(),
    "q+": Math.floor((timestamp.getMonth() + 3) / 3),
    S: timestamp.getMilliseconds(),
    "W+": week[timestamp.getDay()],
  };

  if (!dateValue) {
    return "";
  }

  if (typeof timestamp !== "object") {
    timestamp = new Date(timestamp);
  }
  fmt = fmt || "yyyy-MM-dd";

  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (timestamp.getFullYear() + "").substr(4 - RegExp.$1.length),
    );
  }
  for (k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        // @ts-ignore
        RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length),
      );
    }
  }
  return fmt;
}

export function humanKindDateFormat(dateMills: number) {
  const dayEqual = (a: Date, b: Date) =>
    a.getDate() === b.getDate() &&
    a.getMonth() === b.getMonth() &&
    a.getFullYear() === b.getFullYear();

  const date = new Date(dateMills);
  const today = new Date();
  const yesterday = (() => {
    const d = new Date(today);
    d.setDate(d.getDate() - 1);
    return d;
  })();

  if (dayEqual(date, today)) {
    return dateFormat(date, "HH:mm");
  } else if (dayEqual(date, yesterday)) {
    return dateFormat(date, "昨天 HH:mm");
  } else if (date.getFullYear() === today.getFullYear()) {
    return dateFormat(date, "MM-dd");
  } else {
    return dateFormat(date, "yyyy-MM-dd");
  }
}
