export const URLParams = new URLSearchParams(window.location.search);

export const isTest = import.meta.env.MODE === "development";

export const downLoadFile = (url: string, fileName: string) => {
  const a = document.createElement("a");
  a.target = "_self";
  a.download = fileName;
  a.href = url;
  a.click();
};

export const isEmpty = (value: any) => {
  return value === null || value === undefined || value === "";
};

/** 将参数拼接成url需要的格式 */
export function objToParams(obj: Record<string, any>) {
  return Object.keys(obj)
    .filter((key) => !isEmpty(obj[key]))
    .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)
    .join("&");
}

export function assert(
  condition: boolean,
  message?: string,
): asserts condition {
  if (!condition) {
    throw new Error(message);
  }
}

/** 取数字对应的中文表示 */
export function numZh(num: number): string {
  const dict = ["十", "一", "二", "三", "四", "五", "六", "七", "八", "九"];

  const tens = Math.floor(num / 10);
  const ones = num % 10;

  const t = tens ? (tens === 1 ? "" : dict[tens]) + dict[0] : "";
  const o = ones ? dict[ones] : "";

  return t + o;
}

export function remove<T>(arr: T[], ele: T) {
  const idx = arr.findIndex((e) => e === ele);
  if (idx !== -1) {
    arr.splice(idx, 1);
  }
}

/** 提前调了一次接口进行鉴权，需要复用一下数据减少加载时间 */
export function serverRenderApi<F extends Function>(fn: F) {
  let counter = 0;
  let serverRenderRet: any;

  return function () {
    if (counter === 0) {
      const ret = fn.apply(null, arguments);
      serverRenderRet = ret;
      counter++;
      return ret;
    } else if (counter === 1) {
      counter++;
      return serverRenderRet;
    } else {
      return fn.apply(null, arguments);
    }
  } as unknown as F;
}
