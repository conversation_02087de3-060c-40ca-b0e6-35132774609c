import { ref } from "vue";
import { defineStore } from "pinia";
import { Account } from "@/api/chat";

type accountKeys = keyof Account;
export const useAccountStore = defineStore("accountStore", () => {
  const account = ref<Account>();
  const setAllAccount = (s?: Account) => {
    account.value = s;
  };

  const setSingleAccount = <K extends accountKeys>(
    key: K,
    value: Account[K],
  ) => {
    if (account.value) {
      account.value[key] = value;
    }
  };
  return {
    account,
    setAllAccount,
    setSingleAccount,
  };
});
